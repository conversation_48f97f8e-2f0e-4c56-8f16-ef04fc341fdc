<!DOCTYPE html>
<html class="no-js" lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Component Test - Dynamic Loading | Artix</title>
    
    <link rel="icon" href="assets/img/logo/favicon.png" />
    <link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
    <link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
    <link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/css/style.css" />
    
    <style>
      .component-loading {
        opacity: 0.7;
        min-height: 50px;
        background: var(--section-bg-color);
        border: 2px dashed var(--primary-color);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 10px 0;
        transition: all 0.3s ease;
      }
      
      .component-loaded {
        opacity: 1;
        border: none;
        background: transparent;
      }
      
      .loading-placeholder {
        padding: 20px;
        text-align: center;
        color: var(--primary-color);
        font-weight: bold;
      }
      
      .test-content {
        padding: 100px 0;
        text-align: center;
        min-height: 60vh;
      }
      
      .test-section {
        background: var(--body-bg-color);
        border: 2px solid var(--primary-color);
        border-radius: 10px;
        padding: 40px;
        margin: 40px 0;
      }
      
      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      .status-loading {
        background: #ffa500;
        animation: pulse 1s infinite;
      }
      
      .status-success {
        background: #28a745;
      }
      
      .status-error {
        background: #dc3545;
      }
      
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
      
      .component-info {
        background: var(--section-bg-color);
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: left;
        border: 1px solid var(--border-color);
      }
      
      .component-info h4 {
        color: var(--primary-color);
        margin-bottom: 10px;
      }
      
      .component-info p {
        margin: 5px 0;
        font-size: 14px;
        color: var(--body-color);
      }
      
      .test-button {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        margin: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .test-button:hover {
        opacity: 0.8;
        transform: translateY(-2px);
      }
    </style>
  </head>

  <body class="dark">
    <!-- Dynamic Preloader Placeholder -->
    <div id="preloader-container" class="component-loading">
      <div class="loading-placeholder">🔄 Loading Preloader Component...</div>
    </div>
    
    <!-- Dynamic Header Placeholder -->
    <div id="header-container" class="component-loading">
      <div class="loading-placeholder">🔄 Loading Header Component...</div>
    </div>
    
    <!-- Test Content -->
    <main class="test-content">
      <div class="container">
        <div class="test-section">
          <h1>🧪 Component Loading Test</h1>
          <p class="lead">Testing dynamic loading of header, footer, and preloader components from the same directory</p>
          
          <div class="row">
            <div class="col-md-4">
              <div class="component-info">
                <h4>📋 Preloader Component</h4>
                <p><span id="preloader-status" class="status-indicator status-loading"></span><span id="preloader-text">Loading...</span></p>
                <p><strong>Source:</strong> ./preloader.html</p>
                <p><strong>Expected:</strong> Artix SVG animation</p>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="component-info">
                <h4>🧭 Header Component</h4>
                <p><span id="header-status" class="status-indicator status-loading"></span><span id="header-text">Loading...</span></p>
                <p><strong>Source:</strong> ./header.html</p>
                <p><strong>Expected:</strong> Full navigation + offcanvas</p>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="component-info">
                <h4>🦶 Footer Component</h4>
                <p><span id="footer-status" class="status-indicator status-loading"></span><span id="footer-text">Loading...</span></p>
                <p><strong>Source:</strong> ./footer.html</p>
                <p><strong>Expected:</strong> CTA + contact info + links</p>
              </div>
            </div>
          </div>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <h2>Test Results</h2>
          <div id="test-results">
            <p>⏳ Running component loading tests...</p>
          </div>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <h3>Sample Content</h3>
          <p>This is sample content to test the layout with loaded components.</p>
          <p>The header should appear above this content, and the footer should appear below.</p>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <div class="btn-wrapper">
            <button onclick="reloadComponents()" class="test-button">
              🔄 Reload Components
            </button>
            <button onclick="showComponentSources()" class="test-button">
              📄 View Sources
            </button>
            <button onclick="testIndividually()" class="test-button">
              🔍 Test Individual Components
            </button>
          </div>
        </div>
      </div>
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer-container" class="component-loading">
      <div class="loading-placeholder">🔄 Loading Footer Component...</div>
    </div>

    <!-- Mode Toggle -->
    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
      </svg>
    </span>

    <!-- Scripts -->
    <script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
    <script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
    <script src="assets/js/plugins/swiper.min.js"></script>
    <script src="assets/js/plugins/gsap.js"></script>
    <script src="assets/js/plugins/splittext.js"></script>
    <script src="assets/js/plugins/scrolltigger.js"></script>
    <script src="assets/js/plugins/scrolltoplugins.js"></script>
    <script src="assets/js/plugins/lenis.min.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
      // Simple component loading test
      const components = {
        preloader: {
          container: 'preloader-container',
          source: 'preloader.html',
          status: 'preloader-status',
          text: 'preloader-text'
        },
        header: {
          container: 'header-container',
          source: 'header.html',
          status: 'header-status',
          text: 'header-text'
        },
        footer: {
          container: 'footer-container',
          source: 'footer.html',
          status: 'footer-status',
          text: 'footer-text'
        }
      };

      let results = { preloader: false, header: false, footer: false };

      async function loadComponent(name, config) {
        const container = document.getElementById(config.container);
        const statusEl = document.getElementById(config.status);
        const textEl = document.getElementById(config.text);

        try {
          console.log('Loading', name, 'from', config.source);
          const response = await fetch(config.source);

          if (!response.ok) throw new Error('HTTP ' + response.status);

          const html = await response.text();
          if (!html.trim()) throw new Error('Empty response');

          container.innerHTML = html;
          container.classList.remove('component-loading');
          container.classList.add('component-loaded');

          if (statusEl) statusEl.className = 'status-indicator status-success';
          if (textEl) textEl.textContent = 'Loaded successfully';

          results[name] = true;
          console.log('✅', name, 'loaded successfully');
          return true;

        } catch (error) {
          console.error('❌', name, 'failed:', error);

          if (statusEl) statusEl.className = 'status-indicator status-error';
          if (textEl) textEl.textContent = 'Error: ' + error.message;

          container.innerHTML = '<div style="padding: 20px; background: #f8d7da; color: #721c24; border-radius: 5px;"><h4>⚠️ ' + name + ' Loading Failed</h4><p>Error: ' + error.message + '</p><button onclick="loadComponent(\'' + name + '\', components.' + name + ')" style="background: #721c24; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">🔄 Retry</button></div>';

          results[name] = false;
          return false;
        }
      }

      async function loadAllComponents() {
        console.log('🚀 Starting component loading test...');

        for (const [name, config] of Object.entries(components)) {
          await loadComponent(name, config);
        }

        updateResults();
      }

      function updateResults() {
        const successCount = Object.values(results).filter(r => r).length;
        const totalCount = Object.keys(results).length;

        document.getElementById('test-results').innerHTML =
          '<div style="background: ' + (successCount === totalCount ? '#d4edda' : '#f8d7da') + '; color: ' + (successCount === totalCount ? '#155724' : '#721c24') + '; padding: 15px; border-radius: 8px;"><h4>📊 Test Results</h4><p><strong>Components Loaded:</strong> ' + successCount + '/' + totalCount + '</p><p><strong>Status:</strong> ' + (successCount === totalCount ? '✅ All components loaded successfully!' : '⚠️ Some components failed to load') + '</p></div>';
      }

      window.reloadComponents = function() {
        Object.values(components).forEach(config => {
          const container = document.getElementById(config.container);
          const statusEl = document.getElementById(config.status);
          const textEl = document.getElementById(config.text);

          if (statusEl) statusEl.className = 'status-indicator status-loading';
          if (textEl) textEl.textContent = 'Loading...';
          if (container) {
            container.classList.add('component-loading');
            container.classList.remove('component-loaded');
            container.innerHTML = '<div class="loading-placeholder">🔄 Loading Component...</div>';
          }
        });

        results = { preloader: false, header: false, footer: false };
        loadAllComponents();
      };

      window.showComponentSources = function() {
        alert('Component Sources:\\n\\npreloader: preloader.html\\nheader: header.html\\nfooter: footer.html');
      };

      window.testIndividually = function() {
        Object.entries(components).forEach(([name, config], index) => {
          setTimeout(() => loadComponent(name, config), index * 1000);
        });
      };

      // Start loading when page loads
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🧪 Component Test Page Loaded');
        setTimeout(loadAllComponents, 1000);
      });
    </script>
  </body>
</html>
