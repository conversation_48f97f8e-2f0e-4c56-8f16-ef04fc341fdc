<!DOCTYPE html>
<html class="no-js" lang="en">
  <head>
    <!-- Meta Tags -->
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="author" content="Artix Digital Agency" />
    <meta name="description" content="Artix - Professional digital agency offering exceptional Android & iOS app development, web design, branding, and creative solutions. Transform your digital presence with our innovative services." />
    <meta name="keywords" content="digital agency, app development, web design, branding, creative solutions, mobile apps, android development, ios development, artix" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="Service Details - Artix Digital Agency" />
    <meta property="og:description" content="Discover our exceptional Android & iOS app development services. Professional digital solutions for your business growth." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://artix.co.in/service-details.html" />
    <meta property="og:image" content="assets/img/logo/artix-og-image.png" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Service Details - Artix Digital Agency" />
    <meta name="twitter:description" content="Professional app development and digital solutions by Artix." />
    <meta name="twitter:image" content="assets/img/logo/artix-twitter-image.png" />

    <!-- Favicon Icon -->
    <link rel="icon" href="assets/img/logo/favicon.png" />
    <link rel="apple-touch-icon" href="assets/img/logo/apple-touch-icon.png" />

    <!-- Site Title -->
    <title>Service Details - Android & iOS App Development | Artix Digital Agency</title>

    <!-- Stylesheets -->
    <link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
    <link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
    <link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="assets/css/custom.css" />

    <!-- Custom Styles for Enhanced UX -->
    <style>
      /* Enhanced Preloader Styles */
      .preloader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: var(--body-bg-color);
        z-index: 99999999;
        overflow: hidden;
        transform: translateY(0);
        transition: opacity 0.5s ease-in-out;
      }

      .preloader .txt-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        height: 100%;
        color: var(--heading-color);
      }

      .preloader-text-svg {
        width: 300px;
        height: auto;
        max-width: 80vw;
      }

      .svg-text {
        fill: none;
        stroke: var(--primary-color);
        stroke-width: 2;
        font-size: 120px;
        font-family: var(--heading-font-family);
        font-weight: 700;
        opacity: 0;
        stroke-dasharray: 1000;
        stroke-dashoffset: 1000;
        animation: drawText 3s ease-in-out forwards;
      }

      .loading-percent {
        margin-top: 2rem;
        font-size: 18px;
        font-weight: 500;
        color: var(--body-color);
        opacity: 0;
        animation: fadeInPercent 0.5s ease-in-out 1s forwards;
      }

      @keyframes drawText {
        0% {
          stroke-dashoffset: 1000;
          opacity: 0;
        }
        20% {
          opacity: 1;
        }
        100% {
          stroke-dashoffset: 0;
          opacity: 1;
          fill: var(--primary-color);
        }
      }

      @keyframes fadeInPercent {
        to {
          opacity: 1;
        }
      }

      /* Enhanced Service Details Styling */
      .services-details-title-text {
        position: relative;
        overflow: hidden;
      }

      .services-details-title-text::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 60px;
        height: 3px;
        background: var(--primary-color);
        border-radius: 2px;
      }

      /* Smooth scroll behavior */
      html {
        scroll-behavior: smooth;
      }

      /* Loading animation for images */
      .services-main-img img,
      .image-hov-one img {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-in-out;
      }

      .services-main-img img.loaded,
      .image-hov-one img.loaded {
        opacity: 1;
        transform: translateY(0);
      }

      /* Enhanced responsive design */
      @media (max-width: 768px) {
        .preloader-text-svg {
          width: 250px;
        }

        .svg-text {
          font-size: 80px;
        }

        .loading-percent {
          font-size: 16px;
        }
      }
    </style>
  </head>

  <body class="dark">
    <!-- Enhanced Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#FF4A23" />
                <stop offset="50%" stop-color="#FF6B47" />
                <stop offset="100%" stop-color="#FF4A23" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle">Artix</text>
          </svg>
        </div>
        <div class="loading-percent" id="loadingPercent">0%</div>
      </div>
    </div>
    <!-- End Enhanced Preloader -->

    <!-- Dynamic Header Placeholder -->
    <div id="header"></div>

    <!-- Start Breadcrumb -->
    <div class="ak-height-150 ak-height-lg-120"></div>
    <div class="breadcrumb-area style-2">
      <div class="container">
        <div class="breadcrumb-wapper style-2">
          <div class="breadcrumb-title-box">
            <h1 class="breadcrumb-title">
              Our <span class="highlight-text">Exceptional</span> Android App
              Development
              <span class="highlight-text">Services</span>
            </h1>
            <div class="breadcrumb-caption">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="223"
                  height="12"
                  viewBox="0 0 223 12"
                  fill="none"
                >
                  <path
                    d="M1.33789 1.18359H221.034L209.173 10.9822"
                    stroke="#FF4A23"
                    stroke-linecap="round"
                  ></path>
                </svg>
              </span>
              <span><a href="/">Home</a> / Services </span>
            </div>
          </div>
        </div>
      </div>
      <div class="breadcrumb-stroke">SERVICE</div>
    </div>
    <!-- End Breadcrumb -->

    <!--Start Services Main Images -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <div class="container">
      <div class="services-main-img">
        <img
          src="assets/img/services/services-main-img.png"
          class="img-fluid"
          alt=""
        />
      </div>
    </div>
    <!--End Services Main Images -->

    <!-- Start Services Short Info -->
    <section class="container">
      <div class="services-short-info">
        <div class="services-short-info-content">
          <div class="services-short-info-item">
            <span class="services-short-info-label">Services:</span>
            <span class="services-short-info-text">Android App Dev</span>
          </div>
          <div class="services-short-info-item">
            <span class="services-short-info-label">Approximate Time:</span>
            <span class="services-short-info-text"> 3 Months - 1 Year</span>
          </div>
          <div class="services-short-info-item">
            <span class="services-short-info-label">Industry:</span>
            <span class="services-short-info-text">
              300+ Industry, We are Working</span
            >
          </div>
          <div class="services-short-info-item">
            <span class="services-short-info-label">Area We Cover: </span>
            <span class="services-short-info-text">Around Globe</span>
          </div>
        </div>
      </div>
    </section>
    <!-- End Services Short Info -->

    <!-- Start Services Details Title -->
    <section>
      <div class="container">
        <div class="services-details-title">
          <h3 class="services-details-title-text text-animation">
            Android & IOS App Development
          </h3>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <div class="row">
            <div class="col-md-4 ak-parallax">
              <img
                src="assets/img/services/services-details-show-1.png"
                class="h-100 w-100"
                alt=""
              />
            </div>
            <div class="col-md-8 ak-parallax">
              <img
                src="assets/img/services/services-details-show-2.png"
                class="h-100 w-100"
                alt=""
              />
            </div>
          </div>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
          <div class="ak-height-50 ak-height-lg-50"></div>

          <div class="row align-items-center">
            <div class="col-xl-6 col-12 d-none d-xl-block">
              <div class="image-hov-one">
                <img
                  src="assets/img/services/accordion-1.png"
                  class="img-fluid"
                  alt="..."
                />
              </div>
            </div>
            <div class="col-xl-6 col-12">
              <div class="ak-accordion">
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content active">
                    <h6 class="ak-accordion-title">
                      1. What platforms do you develop mobile apps for?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_32rtyr)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_32rtyr">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content">
                    <h6 class="ak-accordion-title">
                      2. What is the process for developing a mobile app?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_324353)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_324353">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content">
                    <h6 class="ak-accordion-title">
                      3. What platforms do you use for web development?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_32ewrwe)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_32ewrwe">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content">
                    <h6 class="ak-accordion-title">
                      4. How long does it take to build a website?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_3243fgdg)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_3243fgdg">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content">
                    <h6 class="ak-accordion-title">
                      5. How can digital marketing help my business?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_32345fds)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_32345fds">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
                <div class="ak-accordion-item">
                  <div class="ak-accordion-title-content">
                    <h6 class="ak-accordion-title">
                      6. What digital marketing services do you offer?
                    </h6>
                    <span class="accordion-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="11"
                        viewBox="0 0 16 11"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_347_3243gfd)">
                          <path
                            d="M8.00033 1.10913C7.82983 1.10913 7.65916 1.1743 7.529 1.30446L0.862366 7.9711C0.601868 8.23159 0.601868 8.65342 0.862366 8.91376C1.12286 9.17409 1.5447 9.17426 1.80503 8.91376L8.00033 2.71846L14.1956 8.91376C14.4561 9.17426 14.878 9.17426 15.1383 8.91376C15.3986 8.65326 15.3988 8.23143 15.1383 7.9711L8.47166 1.30446C8.34149 1.1743 8.17083 1.10913 8.00033 1.10913Z"
                            fill="#030917"
                          ></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_347_3243gfd">
                            <rect
                              width="16"
                              height="10"
                              fill="white"
                              transform="translate(0.000488281 0.109131)"
                            ></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                  <div class="ak-accordion-tab">
                    It is a long established fact that a reader will be
                    distracted by the readable content of a page when looking at
                    its layout. The point of using Lorem Ipsum is that it has a
                    more-or-less.
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <div class="row">
            <div class="col-md-8 ak-parallax">
              <img
                src="assets/img/services/services-details-show-2.png"
                class="h-100 w-100"
                alt=""
              />
            </div>
            <div class="col-md-4 ak-parallax">
              <img
                src="assets/img/services/services-details-show-1.png"
                class="h-100 w-100"
                alt=""
              />
            </div>
          </div>
          <div class="ak-height-50 ak-height-lg-50"></div>
          <p class="services-details-title-description">
            It is a long established fact that a reader will be distracted by
            the readable content of a page when looking at its layout. The point
            of using Lorem Ipsum is that it has a more-or-less normal
            distribution of letters, as opposed to using 'Content here, content
            here.
          </p>
        </div>
      </div>
    </section>
    <!-- End Services Details Title -->

    <!-- Start Process  -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <section>
      <div class="container">
        <div class="ak-section-heading ak-style-1">
          <div class="ak-section-left">
            <h2 class="ak-section-title text-animation">
              Our <span class="highlight">Exceptional</span> Digital Industrial
              <span class="highlight">Working Process</span>
            </h2>
          </div>
          <div class="ak-section-right">
            <p class="ak-section-desp">
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. has been industry and typesetting of the printing .
            </p>
            <div class="ak-section-caption">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="223"
                  height="12"
                  viewBox="0 0 223 12"
                  fill="none"
                >
                  <path
                    d="M1.33789 1.18359H221.034L209.173 10.9822"
                    stroke="#FF4A23"
                    stroke-linecap="round"
                  />
                </svg>
              </span>
              <span> Process </span>
            </div>
          </div>
        </div>
        <div class="ak-height-75 ak-height-lg-50"></div>
        <div class="funfact-content">
          <div class="funfact style1">
            <div class="funfact-card style-1">
              <div class="funfact-number">
                <span>0</span>
                <span id="count1" class="amin_auto_count">1</span>
              </div>
              <p class="funfact-text text-center">
                Planning and Idea <br />
                Validation
              </p>
            </div>
          </div>
          <div class="funfact style1">
            <div class="funfact-card style-1">
              <div class="funfact-number">
                <span>0</span>
                <span id="count2" class="amin_auto_count">02</span>
              </div>
              <p class="funfact-text text-center">
                Wireframing and <br />
                Design
              </p>
            </div>
          </div>

          <div class="funfact style1">
            <div class="funfact-card style-1">
              <div class="funfact-number">
                <span>0</span>
                <span id="count3" class="amin_auto_count">03</span>
              </div>
              <p class="funfact-text text-center">
                Technical Planning & <br />
                Development
              </p>
            </div>
          </div>
          <div class="funfact style1">
            <div class="funfact-card style-1">
              <div class="funfact-number">
                <span>0</span>
                <span id="count4" class="amin_auto_count">04</span>
              </div>
              <p class="funfact-text text-center">
                Testing, Deployment <br />
                Regular Update
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Start Process  -->

    <!-- Start Services Details Cta-->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <div class="container">
      <div class="services-details-cta">
        <div class="dot-top-left"></div>
        <div class="dot-top-right"></div>
        <div class="dot-bottom-left"></div>
        <div class="dot-bottom-right"></div>
        <div class="services-bg-start">
          <img
            src="assets/img/shape/cta-start-left.png"
            class="ak-parallax"
            alt="..."
          />
          <img
            src="assets/img/shape/cta-start-right.png"
            class="ak-parallax"
            alt="..."
          />
        </div>
        <div class="services-details-cta-wapper">
          <div class="services-details-cta-content">
            <h2 class="services-details-cta-title text-animation">
              Get in Touch to Bring Your
              <span class="highlight text-underlines underline-anim"
                >Project</span
              >
              to Life!
            </h2>
          </div>
        </div>
        <div class="services-details-cta-btn">
          <a href="contact.html" class="circle-btn style-1 circle-btn-anim">
            <span class="text text-uppercase">
              Start
              <i class="flaticon-up-right-arrow"></i>
              <br />
              Project
            </span>
          </a>
        </div>
      </div>
    </div>
    <!-- End Services Details Cta-->

    <!-- Start Moving text -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <div class="slideing-text-content style2">
      <p class="slideing-text text-color-three">
        Design / Product Development / Brand Design
      </p>
      <p class="slideing-text text-color-two">
        Digital Design / Product Design / Brand Design
      </p>
    </div>
    <!-- End Moving text -->

    <!-- Start Newsletter -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <section class="container">
      <div class="newsletter-content style-2">
        <div class="newsletter-title-content">
          <h2 class="newsletter-title text-animation">
            Join Our
            <span class="highlight text-underlines underline-anim"
              >Newsletter</span
            >
            for the Latest <span class="highlight">Exclusive</span> Content
          </h2>
        </div>

        <form class="newsletter-form">
          <input
            type="email"
            class="newsletter-input style-2"
            placeholder="Enter your email..."
            required
          />
          <button type="submit" class="newsletter-btn">
            <span class="newbtn-text"> Newsletter </span>
            <span class="primary-icon-anim">
              <i class="flaticon-up-right-arrow"></i>
              <i class="flaticon-up-right-arrow"></i>
            </span>
          </button>
        </form>
      </div>
    </section>
    <!-- End Newsletter -->

    <div id="footer"></div>

    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <span class="ak-scrollup">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z"
          fill="currentColor"
        />
      </svg>
    </span>
    <!-- Start Video Popup -->
    <div class="ak-video-popup">
      <div class="ak-video-popup-overlay"></div>
      <div class="ak-video-popup-content">
        <div class="ak-video-popup-layer"></div>
        <div class="ak-video-popup-container">
          <div class="ak-video-popup-align">
            <div class="embed-responsive embed-responsive-16by9">
              <iframe class="embed-responsive-item" src="about:blank"></iframe>
            </div>
          </div>
          <div class="ak-video-popup-close"></div>
        </div>
      </div>
    </div>
    <!-- End Video Popup -->

    <!-- Script -->
    <script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
    <script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
    <script src="assets/js/plugins/swiper.min.js"></script>

    <script src="assets/js/plugins/gsap.js"></script>
    <script src="assets/js/plugins/lenis.min.js"></script>
    <script src="assets/js/plugins/splittext.js"></script>
    <script src="assets/js/plugins/scrolltigger.js"></script>
    <script src="assets/js/plugins/scrolltoplugins.js"></script>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/component-loader.js"></script>

    <!-- Enhanced Service Details JavaScript -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Enhanced Preloader with Progress Animation
        const preloader = document.getElementById('preloader');
        const loadingPercent = document.getElementById('loadingPercent');
        const svgText = document.getElementById('svgText');

        if (preloader && loadingPercent) {
          let progress = 0;
          const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
              progress = 100;
              clearInterval(progressInterval);

              // Hide preloader after animation completes
              setTimeout(() => {
                preloader.style.opacity = '0';
                setTimeout(() => {
                  preloader.style.display = 'none';
                  // Trigger image loading animations
                  initImageAnimations();
                }, 500);
              }, 500);
            }
            loadingPercent.textContent = Math.floor(progress) + '%';
          }, 100);
        }

        // Image Loading Animations
        function initImageAnimations() {
          const images = document.querySelectorAll('.services-main-img img, .image-hov-one img');

          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                entry.target.classList.add('loaded');
                imageObserver.unobserve(entry.target);
              }
            });
          }, {
            threshold: 0.1,
            rootMargin: '50px'
          });

          images.forEach(img => {
            if (img.complete) {
              img.classList.add('loaded');
            } else {
              img.addEventListener('load', () => {
                imageObserver.observe(img);
              });
            }
          });
        }

        // Enhanced Accordion Functionality
        const accordionItems = document.querySelectorAll('.ak-accordion-item');

        accordionItems.forEach(item => {
          const titleContent = item.querySelector('.ak-accordion-title-content');
          const tab = item.querySelector('.ak-accordion-tab');

          titleContent.addEventListener('click', () => {
            // Close all other accordion items
            accordionItems.forEach(otherItem => {
              if (otherItem !== item) {
                otherItem.querySelector('.ak-accordion-title-content').classList.remove('active');
                const otherTab = otherItem.querySelector('.ak-accordion-tab');
                otherTab.style.maxHeight = null;
              }
            });

            // Toggle current item
            titleContent.classList.toggle('active');

            if (titleContent.classList.contains('active')) {
              tab.style.maxHeight = tab.scrollHeight + 'px';
            } else {
              tab.style.maxHeight = null;
            }
          });
        });

        // Smooth Scroll for Internal Links
        const internalLinks = document.querySelectorAll('a[href^="#"]');

        internalLinks.forEach(link => {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          });
        });

        // Enhanced Page Performance Tracking
        if ('performance' in window) {
          window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`Page loaded in ${Math.round(loadTime)}ms`);

            // Track Core Web Vitals if available
            if ('web-vitals' in window) {
              // This would require the web-vitals library
              // getCLS(console.log);
              // getFID(console.log);
              // getLCP(console.log);
            }
          });
        }

        // Service Details Enhancement
        const serviceDetails = document.querySelector('.services-details-title');
        if (serviceDetails) {
          // Add reading progress indicator
          const progressBar = document.createElement('div');
          progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: var(--primary-color);
            z-index: 9999;
            transition: width 0.3s ease;
          `;
          document.body.appendChild(progressBar);

          window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
          });
        }
      });

      // Component Loading Event Listeners
      document.addEventListener('componentLoaded', function(event) {
        const { elementId } = event.detail;

        if (elementId === 'header') {
          // Initialize header-specific functionality
          console.log('Header loaded and ready');

          // Add active class to current page navigation
          const currentPage = window.location.pathname.split('/').pop();
          const navLinks = document.querySelectorAll('.ak-nav_list a');

          navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPage) {
              link.parentElement.classList.add('current-menu-item');
            }
          });
        }

        if (elementId === 'footer') {
          // Initialize footer-specific functionality
          console.log('Footer loaded and ready');
        }
      });

      // Error Handling for Component Loading
      document.addEventListener('componentLoadError', function(event) {
        console.warn('Component failed to load:', event.detail);

        // Provide fallback functionality
        if (event.detail.elementId === 'header') {
          // Show a minimal navigation fallback
          const headerElement = document.getElementById('header');
          if (headerElement) {
            headerElement.innerHTML = `
              <header style="padding: 20px; background: var(--body-bg-color); border-bottom: 1px solid var(--border-color);">
                <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                  <a href="index.html" style="font-size: 24px; font-weight: bold; color: var(--primary-color); text-decoration: none;">Artix</a>
                  <nav>
                    <a href="index.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Home</a>
                    <a href="about.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">About</a>
                    <a href="services.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Services</a>
                    <a href="contact.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Contact</a>
                  </nav>
                </div>
              </header>
            `;
          }
        }
      });
    </script>
  </body>
</html>