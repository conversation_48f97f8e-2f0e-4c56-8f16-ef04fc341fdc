<!DOCTYPE html>
<html class="no-js" lang="en">
  <head>
    <!-- Meta Tags -->
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="author" content="Thememarch" />
    <!-- Site Title -->
    <title>Artix - Branding | Creative | Designs</title>

    <!-- Embedded Styles -->
  </head>

  <body class="dark">
    <!-- Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#000" />
                <stop offset="50%" stop-color="#000" />
                <stop offset="100%" stop-color="#000" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText">Artix</text>
          </svg>
        </div>
        <div class="loading-percent">0%</div>
      </div>
    </div>
    <!-- Preloader -->

    <!-- Start Upcomming Section -->
    <div class="upcomming-soon-container">
      <div class="ak-height-150 ak-height-lg-80"></div>
      <div class="ak-center">
        <div class="upcomming-soon-wapper">
          <div class="theme-border-wrap upcomming-soon-border">
            <div class="b-top-left type2">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-top-right type2 d-flex">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-right type2 d-flex flex-end">
              <div class="horizontal flex-end align-self-end"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-left type2">
              <div class="verticle"></div>
              <div class="horizontal"></div>
            </div>
            <div class="container">
              <div class="brand-logo ak-center">
                <div class="offcanvas-logo-content">
                  <a class="ak-site_branding dark-logo" href="index.html">
                    <img src="assets/img/logo/dark-logo.png" alt="..." />
                  </a>
                  <a class="ak-site_branding white-logo" href="index.html">
                    <img src="assets/img/logo/white-logo.png" alt="..." />
                  </a>
                </div>
              </div>
              <div class="ak-height-75 ak-height-lg-50"></div>
              <div class="upcomming-soon-content">
                <div class="time-counter">
                  <div class="funfact-content">
                    <div class="funfact style1">
                      <div class="funfact-card style-1">
                        <div class="funfact-number" id="months">
                          <span>03</span>
                        </div>
                        <p class="funfact-text">Months</p>
                      </div>
                    </div>
                    <div class="funfact style1">
                      <div class="funfact-card style-1">
                        <div class="funfact-number" id="days">
                          <span>27</span>
                        </div>
                        <p class="funfact-text">Days</p>
                      </div>
                    </div>
                    <div class="funfact style1">
                      <div class="funfact-card style-1">
                        <div class="funfact-number" id="hours">
                          <span>23</span>
                        </div>
                        <p class="funfact-text">Hours</p>
                      </div>
                    </div>
                    <div class="funfact style1">
                      <div class="funfact-card style-1">
                        <div class="funfact-number" id="minutes">
                          <span>56</span>
                        </div>
                        <p class="funfact-text">Minutes</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="upcomming-soon-info">
                  <h3 class="upcomming-soon-title">
                    Coming <span class="highlight">Soon</span>
                  </h3>
                  <p class="upcomming-soon-desp">
                    We're working hard to bring you an exciting new experience.
                    Stay tuned!
                  </p>
                </div>
              </div>
              <div class="ak-height-75 ak-height-lg-50"></div>
            </div>
          </div>
          <div class="social-wapper">
            <div class="social-content">
              <h5 class="social-title">
                Join Our
                <span class="heghtlight underline">Newsletter</span> for the
                Latest <span class="heghtlight">Exclusive</span> Content
              </h5>
              <div class="social-icon">
                <a href="#" class="icon style-2">
                  <i class="flaticon-facebook"></i>
                </a>
                <a href="#" class="icon style-2">
                  <i class="flaticon-linkedin"></i>
                </a>
                <a href="#" class="icon style-2">
                  <i class="flaticon-instagram-logo"></i>
                </a>
                <a href="#" class="icon style-2">
                  <i class="flaticon-twitter"></i>
                </a>
              </div>
            </div>
            <div class="email-send-form">
              <form>
                <input
                  required
                  type="text"
                  name="email"
                  id="email"
                  placeholder="Enter your email..."
                />

                <div class="email-send-btn">
                  <button type="submit" class="more-btn style3 border-0">
                    <span class="morebtn-text"> Newsletter </span>
                    <span class="primary-icon-anim">
                      <i class="flaticon-up-right-arrow"></i>
                      <i class="flaticon-up-right-arrow"></i>
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="ak-height-150 ak-height-lg-80"></div>
    </div>
    <!-- End Upcomming Section -->

    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <!-- Script -->
    <script>
  // Set your target launch date here
  const targetDate = new Date("2025-08-14T00:00:00");

  function updateCountdown() {
      const now = new Date();
      let timeLeft = Math.max(0, targetDate - now);

      // Calculate total values
      let totalSeconds = Math.floor(timeLeft / 1000);

      // Extract each time unit
      const totalDays = Math.floor(totalSeconds / (24 * 3600));
      const monthsCount = Math.floor(totalDays / 30);
      const daysCount = totalDays % 30;

      totalSeconds = totalSeconds % (24 * 3600);
      const hoursCount = Math.floor(totalSeconds / 3600);
      
      totalSeconds = totalSeconds % 3600;
      const minutesCount = Math.floor(totalSeconds / 60);

      // Update display with padded numbers
      document.querySelector("#months span").textContent = String(monthsCount).padStart(2, "0");
      document.querySelector("#days span").textContent = String(daysCount).padStart(2, "0");
      document.querySelector("#hours span").textContent = String(hoursCount).padStart(2, "0");
      document.querySelector("#minutes span").textContent = String(minutesCount).padStart(2, "0");

      // Check if countdown is finished
      if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          document.querySelector('.time-counter').innerHTML = "<h2>Website Launched!</h2>";
      }
  }

  // Initial call
  updateCountdown();

  // Update every second
  const countdownInterval = setInterval(updateCountdown, 1000);
</script>

    <script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
    <script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
    <script src="assets/js/plugins/swiper.min.js"></script>

    <script src="assets/js/plugins/gsap.js"></script>
    <script src="assets/js/plugins/lenis.min.js"></script>
    <script src="assets/js/plugins/splittext.js"></script>
    <script src="assets/js/plugins/scrolltigger.js"></script>
    <script src="assets/js/plugins/scrolltoplugins.js"></script>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/include.js"></script>
    <script src="assets/js/countdown.js"></script>
  </body>
</html>
