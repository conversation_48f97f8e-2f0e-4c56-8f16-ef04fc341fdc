<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
<!-- Master Head Template for All Pages (Except index.html) -->
<!-- This template provides consistent SEO, branding, and styling across all pages -->

<!-- Meta Tags -->
<meta charset="utf-8" />
<meta http-equiv="x-ua-compatible" content="ie=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="author" content="Artix Digital Agency" />
<meta name="description" content="Explore Artix's portfolio showcasing our best creative work, successful projects, and innovative digital solutions for clients worldwide." />
<meta name="keywords" content="portfolio, creative work, projects, case studies, web design portfolio, app development showcase" />
<meta name="robots" content="index, follow" />

<!-- Open Graph Meta Tags for Social Media -->
<meta property="og:title" content="Portfolio - Our Creative Work & Projects - Artix Digital Agency" />
<meta property="og:description" content="Explore Artix's portfolio showcasing our best creative work, successful projects, and innovative digital solutions for clients worldwide." />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://artix.co.in/portfolio.html" />
<meta property="og:image" content="assets/img/logo/artix-og-image.png" />
<meta property="og:site_name" content="Artix Digital Agency" />

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="Portfolio - Our Creative Work & Projects - Artix Digital Agency" />
<meta name="twitter:description" content="Explore Artix's portfolio showcasing our best creative work, successful projects, and innovative digital solutions for clients worldwide." />
<meta name="twitter:image" content="assets/img/logo/artix-twitter-image.png" />
<meta name="twitter:site" content="@ArtixDigital" />

<!-- Favicon and Touch Icons -->
<link rel="icon" href="assets/img/logo/favicon.png" />
<link rel="apple-touch-icon" href="assets/img/logo/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="assets/img/logo/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="assets/img/logo/favicon-16x16.png" />

<!-- Site Title -->
<title>Portfolio - Our Creative Work & Projects | Artix - Branding | Creative | Designs</title>

<!-- Preload Critical Resources -->
<link rel="preload" href="assets/css/style.css" as="style" />
<link rel="preload" href="assets/js/main.js" as="script" />
<link rel="preload" href="assets/fonts/flaticon_cretio.css" as="style" />

<!-- DNS Prefetch for External Resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />

<!-- Stylesheets -->
<link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
<link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
<link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
<link rel="stylesheet" href="assets/css/style.css" />
<link rel="stylesheet" href="assets/css/custom.css" />

<!-- Enhanced Global Styles -->
<style>
  /* Global Enhanced Preloader Styles */
  .preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--body-bg-color);
    z-index: 99999999;
    overflow: hidden;
    transform: translateY(0);
    transition: opacity 0.5s ease-in-out;
  }
  
  .preloader .txt-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
    color: var(--heading-color);
  }
  
  .preloader-text-svg {
    width: 300px;
    height: auto;
    max-width: 80vw;
  }
  
  .svg-text {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 2;
    font-size: 120px;
    font-family: var(--heading-font-family);
    font-weight: 700;
    opacity: 0;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawText 3s ease-in-out forwards;
  }
  
  .loading-percent {
    margin-top: 2rem;
    font-size: 18px;
    font-weight: 500;
    color: var(--body-color);
    opacity: 0;
    animation: fadeInPercent 0.5s ease-in-out 1s forwards;
  }
  
  @keyframes drawText {
    0% {
      stroke-dashoffset: 1000;
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      stroke-dashoffset: 0;
      opacity: 1;
      fill: var(--primary-color);
    }
  }
  
  @keyframes fadeInPercent {
    to {
      opacity: 1;
    }
  }
  
  /* Global Page Enhancement Styles */
  .page-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .page-content.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Reading Progress Bar */
  .reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #FF6B47);
    z-index: 9999;
    transition: width 0.3s ease;
  }
  
  /* Global Image Loading Animation */
  .img-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .img-animate.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Smooth Scroll */
  html {
    scroll-behavior: smooth;
  }
  
  /* Global Responsive Enhancements */
  @media (max-width: 768px) {
    .preloader-text-svg {
      width: 250px;
    }
    
    .svg-text {
      font-size: 80px;
    }
    
    .loading-percent {
      font-size: 16px;
    }
  }
  
  @media (max-width: 480px) {
    .preloader-text-svg {
      width: 200px;
    }
    
    .svg-text {
      font-size: 60px;
    }
  }
  
  /* Global Performance Optimizations */
  * {
    box-sizing: border-box;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* Global Accessibility Improvements */
  @media (prefers-reduced-motion: reduce) {
    .svg-text {
      animation: none;
      opacity: 1;
      stroke-dashoffset: 0;
      fill: var(--primary-color);
    }
    
    .loading-percent {
      animation: none;
      opacity: 1;
    }
    
    .page-content,
    .img-animate {
      transition: none;
    }
  }
</style>

<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Artix Digital Agency",
  "url": "https://artix.co.in",
  "logo": "https://artix.co.in/assets/img/logo/dark-logo.png",
  "description": "Professional digital agency offering branding, creative designs, web development, and mobile app development services.",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "IN",
    "addressLocality": "India"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+91 81065 34629",
    "contactType": "customer service",
    "email": "<EMAIL>"
  },
  "sameAs": [
    "https://facebook.com/artixdigital",
    "https://twitter.com/artixdigital",
    "https://linkedin.com/company/artixdigital"
  ]
}
</script>

</head>

<body class="dark">
    <!-- Enhanced Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#FF4A23" />
                <stop offset="50%" stop-color="#FF6B47" />
                <stop offset="100%" stop-color="#FF4A23" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle">Artix</text>
          </svg>
        </div>
        <div class="loading-percent" id="loadingPercent">0%</div>
      </div>
    </div>
    <!-- End Enhanced Preloader -->
    
    <!-- Dynamic Header Placeholder -->
    <div id="header"></div>
    
    <!-- Page Content -->
    <main class="page-content">
<!-- Start Breadcrumb -->
    <div class="ak-height-150 ak-height-lg-120"></div>
    <div class="breadcrumb-area style-2">
      <div class="container">
        <div class="breadcrumb-wapper style-2">
          <div class="breadcrumb-title-box">
            <h1 class="breadcrumb-title">
              Our <span class="highlight-text">Exceptional</span> Successful
              Development
              <span class="highlight-text">Projects</span>
            </h1>
            <div class="breadcrumb-caption">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="223"
                  height="12"
                  viewBox="0 0 223 12"
                  fill="none"
                >
                  <path
                    d="M1.33789 1.18359H221.034L209.173 10.9822"
                    stroke="#FF4A23"
                    stroke-linecap="round"
                  ></path>
                </svg>
              </span>
              <span><a href="/">Home</a> / Projects </span>
            </div>
          </div>
        </div>
      </div>
      <div class="breadcrumb-stroke">PORTFOLIO</div>
    </div>
    <!-- End Breadcrumb -->

    <div class="ak-height-150 ak-height-lg-80"></div>
    <div class="container">
      <div class="portfolio-wapper">
        <div class="row justify-content-between align-items-center g-5  overflow-hidden">
          <div class="col-md-5">
            <a href="portfolio-details.html" class="portfolio-card   overflow-hidden">
              <div
                class="portfolio-top-img img-anim-left-show"
                data-delay="2.5"
              >
                <img
                  src="assets/img/portfolio/portfolio-top-img-1.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Empowering Brands Redefining Digital Excellence
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-7">
            <a href="portfolio-details.html" class="portfolio-card  overflow-hidden">
              <div
                class="portfolio-top-img img-anim-left-show"
                data-delay="2.5"
              >
                <img
                  src="assets/img/portfolio/portfolio-top-img-2.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Shaping the Future of Digital <br />
                  Innovation and Leadership
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-5">
            <a href="portfolio-details.html" class="portfolio-card   overflow-hidden">
              <div class="portfolio-top-img img-anim-left-show">
                <img
                  src="assets/img/portfolio/portfolio-top-img-3.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Pioneering Innovation and Redefining Standards
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-7">
            <a href="portfolio-details.html" class="portfolio-card  overflow-hidden">
              <div class="portfolio-top-img img-anim-left-show">
                <img
                  src="assets/img/portfolio/portfolio-top-img-4.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Elevating Digital Frontiers: Crafting Strategic <br />
                  Online Solutions
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-12">
            <a href="portfolio-details.html" class="portfolio-card  overflow-hidden">
              <div class="portfolio-top-img img-anim-left-show">
                <img
                  src="assets/img/portfolio/portfolio-top-img-5.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Driving Digital Transformation: Pioneering Strategies to
                  Redesign Digital Horizons and <br />
                  Define New Standards in Online Innovation
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-6">
            <a href="portfolio-details.html" class="portfolio-card  overflow-hidden">
              <div class="portfolio-top-img img-anim-left-show">
                <img
                  src="assets/img/portfolio/portfolio-top-img-6.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title">
                  Product Launch Camp aign for EcoHome
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-6">
            <a href="portfolio-details.html" class="portfolio-card  overflow-hidden">
              <div class="portfolio-top-img img-anim-left-show">
                <img
                  src="assets/img/portfolio/portfolio-top-img-7.png"
                  alt="..."
                  class="w-100"
                />
              </div>
              <div class="portfolio-content">
                <h6 class="portfolio-title ak-w-60">
                  Transforming Brands Through Strategic Initiatives
                </h6>
                <div class="portfolio-btn">
                  <i class="flaticon-up-right-arrow"></i>
                </div>
              </div>
            </a>
          </div>
        </div>
        <div class="ak-height-150 ak-height-lg-80"></div>
        <div class="ak-center">
          <a href="#" class="circle-btn circle-btn-anim">
            <span class="text text-uppercase">
              Load More
              <br />
              Project
              <i class="flaticon-up-right-arrow"></i>
            </span>
          </a>
        </div>
      </div>
    </div>

    

    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <span class="ak-scrollup">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z"
          fill="currentColor"
        />
      </svg>
    </span>
    <!-- Start Video Popup -->
    <div class="ak-video-popup">
      <div class="ak-video-popup-overlay"></div>
      <div class="ak-video-popup-content">
        <div class="ak-video-popup-layer"></div>
        <div class="ak-video-popup-container">
          <div class="ak-video-popup-align">
            <div class="embed-responsive embed-responsive-16by9">
              <iframe class="embed-responsive-item" src="about:blank"></iframe>
            </div>
          </div>
          <div class="ak-video-popup-close"></div>
        </div>
      </div>
    </div>
    <!-- End Video Popup -->

    <!-- Script -->
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer"></div>
    
    <!-- Mode Toggle -->
    <div class="mode-toggle">
        <div class="setting-mode">
            <button id="open">
                <i class="flaticon-sun"></i>
            </button>
            <button id="clecel">
                <i class="flaticon-close-button-1"></i>
            </button>
        </div>
        <div class="mode-btn js-mode-type">
            <button data-mode="light" class="mode-light">
                <i class="flaticon-sun"></i>
            </button>
            <button data-mode="dark" class="active mode-dark">
                <i class="flaticon-night-mode"></i>
            </button>
        </div>
    </div>
    
    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
        </svg>
    </span>
    
    <!-- Video Popup -->
    <div class="ak-video-popup">
        <div class="ak-video-popup-overlay"></div>
        <div class="ak-video-popup-content">
            <div class="ak-video-popup-layer"></div>
            <div class="ak-video-popup-container">
                <div class="ak-video-popup-align">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="ak-video-popup-close"></div>
            </div>
        </div>
    </div>
    
<!-- Master JavaScript Template for All Pages (Except index.html) -->
<!-- This provides consistent functionality across all pages -->

<!-- Core Scripts -->
<script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
<script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
<script src="assets/js/plugins/swiper.min.js"></script>
<script src="assets/js/plugins/gsap.js"></script>
<script src="assets/js/plugins/lenis.min.js"></script>
<script src="assets/js/plugins/splittext.js"></script>
<script src="assets/js/plugins/scrolltigger.js"></script>
<script src="assets/js/plugins/scrolltoplugins.js"></script>
<script src="assets/js/main.js"></script>
<script src="assets/js/component-loader.js"></script>

<!-- Global Enhanced Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Global Variables
  const pageType = 'portfolio';
  const pageName = 'Portfolio';
  
  // Enhanced Preloader with Progress Animation
  const preloader = document.getElementById('preloader');
  const loadingPercent = document.getElementById('loadingPercent');
  const svgText = document.getElementById('svgText');
  
  if (preloader && loadingPercent) {
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(progressInterval);
        
        // Hide preloader after animation completes
        setTimeout(() => {
          preloader.style.opacity = '0';
          setTimeout(() => {
            preloader.style.display = 'none';
            // Initialize page content animations
            initPageAnimations();
          }, 500);
        }, 500);
      }
      loadingPercent.textContent = Math.floor(progress) + '%';
    }, 100);
  }
  
  // Global Page Animations
  function initPageAnimations() {
    // Fade in main content
    const pageContent = document.querySelector('.page-content') || document.querySelector('main');
    if (pageContent) {
      pageContent.classList.add('loaded');
    }
    
    // Initialize image animations
    initImageAnimations();
    
    // Initialize reading progress
    initReadingProgress();
    
    // Initialize smooth scrolling
    initSmoothScrolling();
  }
  
  // Global Image Loading Animations
  function initImageAnimations() {
    const images = document.querySelectorAll('img:not(.no-animate)');
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('img-animate');
          setTimeout(() => {
            entry.target.classList.add('loaded');
          }, 100);
          imageObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });
    
    images.forEach(img => {
      if (img.complete && img.naturalHeight !== 0) {
        img.classList.add('img-animate', 'loaded');
      } else {
        img.addEventListener('load', () => {
          imageObserver.observe(img);
        });
        // Fallback for images that might already be cached
        if (img.complete) {
          imageObserver.observe(img);
        }
      }
    });
  }
  
  // Global Reading Progress Bar
  function initReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.body.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      progressBar.style.width = Math.min(scrollPercent, 100) + '%';
    });
  }
  
  // Global Smooth Scrolling for Internal Links
  function initSmoothScrolling() {
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }
  
  // Global Accordion Enhancement (if present)
  const accordionItems = document.querySelectorAll('.ak-accordion-item');
  if (accordionItems.length > 0) {
    accordionItems.forEach(item => {
      const titleContent = item.querySelector('.ak-accordion-title-content');
      const tab = item.querySelector('.ak-accordion-tab');
      
      if (titleContent && tab) {
        titleContent.addEventListener('click', () => {
          // Close all other accordion items
          accordionItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.querySelector('.ak-accordion-title-content').classList.remove('active');
              const otherTab = otherItem.querySelector('.ak-accordion-tab');
              if (otherTab) otherTab.style.maxHeight = null;
            }
          });
          
          // Toggle current item
          titleContent.classList.toggle('active');
          
          if (titleContent.classList.contains('active')) {
            tab.style.maxHeight = tab.scrollHeight + 'px';
          } else {
            tab.style.maxHeight = null;
          }
        });
      }
    });
  }
  
  // Global Performance Tracking
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      console.log(`${pageName} page loaded in ${Math.round(loadTime)}ms`);
      
      // Send analytics if available
      if (typeof gtag !== 'undefined') {
        gtag('event', 'page_load_time', {
          event_category: 'Performance',
          event_label: pageName,
          value: Math.round(loadTime)
        });
      }
    });
  }
  
  // Global Error Handling
  window.addEventListener('error', (e) => {
    console.error('Global error on', pageName, ':', e.error);
    
    // Send error to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: e.error.toString(),
        fatal: false
      });
    }
  });
});

// Component Loading Event Listeners
document.addEventListener('componentLoaded', function(event) {
  const { elementId } = event.detail;
  
  if (elementId === 'header') {
    // Initialize header-specific functionality
    console.log('Header loaded and ready');
    
    // Add active class to current page navigation
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.ak-nav_list a');
    
    navLinks.forEach(link => {
      const linkHref = link.getAttribute('href');
      if (linkHref === currentPage || 
          (currentPage === 'index.html' && linkHref === '/') ||
          (currentPage === '' && linkHref === 'index.html')) {
        link.parentElement.classList.add('current-menu-item');
      }
    });
    
    // Initialize mobile menu if present
    const mobileMenuToggle = document.querySelector('.offcanvaopen-btn');
    if (mobileMenuToggle) {
      console.log('Mobile menu initialized');
    }
  }
  
  if (elementId === 'footer') {
    // Initialize footer-specific functionality
    console.log('Footer loaded and ready');
    
    // Update copyright year
    const copyrightYear = document.querySelector('.copyright-year');
    if (copyrightYear) {
      copyrightYear.textContent = new Date().getFullYear();
    }
  }
});

// Error Handling for Component Loading
document.addEventListener('componentLoadError', function(event) {
  console.warn('Component failed to load:', event.detail);
  
  // Provide fallback functionality
  if (event.detail.elementId === 'header') {
    const headerElement = document.getElementById('header');
    if (headerElement) {
      headerElement.innerHTML = `
        <header style="padding: 20px; background: var(--body-bg-color); border-bottom: 1px solid var(--border-color);">
          <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
            <a href="index.html" style="font-size: 24px; font-weight: bold; color: var(--primary-color); text-decoration: none;">Artix</a>
            <nav>
              <a href="index.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Home</a>
              <a href="about.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">About</a>
              <a href="services.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Services</a>
              <a href="portfolio.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Portfolio</a>
              <a href="contact.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Contact</a>
            </nav>
          </div>
        </header>
      `;
    }
  }
  
  if (event.detail.elementId === 'footer') {
    const footerElement = document.getElementById('footer');
    if (footerElement) {
      footerElement.innerHTML = `
        <footer style="padding: 40px 20px; background: var(--body-bg-color); border-top: 1px solid var(--border-color); text-align: center;">
          <div style="max-width: 1200px; margin: 0 auto;">
            <p style="color: var(--body-color); margin: 0;">&copy; ${new Date().getFullYear()} Artix Digital Agency. All rights reserved.</p>
          </div>
        </footer>
      `;
    }
  }
});

// Global Utility Functions
window.ArtixGlobal = {
  // Smooth scroll to element
  scrollTo: function(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },
  
  // Show loading state
  showLoading: function(element) {
    if (element) {
      element.style.opacity = '0.5';
      element.style.pointerEvents = 'none';
    }
  },
  
  // Hide loading state
  hideLoading: function(element) {
    if (element) {
      element.style.opacity = '1';
      element.style.pointerEvents = 'auto';
    }
  },
  
  // Get page info
  getPageInfo: function() {
    return {
      type: 'portfolio',
      name: 'Portfolio',
      url: window.location.href,
      title: document.title
    };
  }
};
</script>

</body>
</html>