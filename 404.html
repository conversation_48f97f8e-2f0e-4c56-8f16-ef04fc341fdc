<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
<!-- Master Head Template for All Pages (Except index.html) -->
<!-- This template provides consistent SEO, branding, and styling across all pages -->

<!-- Meta Tags -->
<meta charset="utf-8" />
<meta http-equiv="x-ua-compatible" content="ie=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="author" content="Artix Digital Agency" />
<meta name="description" content="The page you're looking for doesn't exist. Return to Artix Digital Agency homepage or explore our services." />
<meta name="keywords" content="404 error, page not found, artix digital agency" />
<meta name="robots" content="index, follow" />

<!-- Open Graph Meta Tags for Social Media -->
<meta property="og:title" content="Page Not Found - 404 Error - Artix Digital Agency" />
<meta property="og:description" content="The page you're looking for doesn't exist. Return to Artix Digital Agency homepage or explore our services." />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://artix.co.in/error.html" />
<meta property="og:image" content="assets/img/logo/artix-og-image.png" />
<meta property="og:site_name" content="Artix Digital Agency" />

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="Page Not Found - 404 Error - Artix Digital Agency" />
<meta name="twitter:description" content="The page you're looking for doesn't exist. Return to Artix Digital Agency homepage or explore our services." />
<meta name="twitter:image" content="assets/img/logo/artix-twitter-image.png" />
<meta name="twitter:site" content="@ArtixDigital" />

<!-- Favicon and Touch Icons -->
<link rel="icon" href="assets/img/logo/favicon.png" />
<link rel="apple-touch-icon" href="assets/img/logo/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="assets/img/logo/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="assets/img/logo/favicon-16x16.png" />

<!-- Site Title -->
<title>Page Not Found - 404 Error | Artix - Branding | Creative | Designs</title>

<!-- Preload Critical Resources -->
<link rel="preload" href="assets/css/style.css" as="style" />
<link rel="preload" href="assets/js/main.js" as="script" />
<link rel="preload" href="assets/fonts/flaticon_cretio.css" as="style" />

<!-- DNS Prefetch for External Resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />

<!-- Stylesheets -->
<link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
<link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
<link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
<link rel="stylesheet" href="assets/css/style.css" />
<link rel="stylesheet" href="assets/css/custom.css" />

<!-- Enhanced Global Styles -->
<style>
  /* Global Enhanced Preloader Styles */
  .preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--body-bg-color);
    z-index: 99999999;
    overflow: hidden;
    transform: translateY(0);
    transition: opacity 0.5s ease-in-out;
  }
  
  .preloader .txt-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
    color: var(--heading-color);
  }
  
  .preloader-text-svg {
    width: 300px;
    height: auto;
    max-width: 80vw;
  }
  
  .svg-text {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 2;
    font-size: 120px;
    font-family: var(--heading-font-family);
    font-weight: 700;
    opacity: 0;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawText 3s ease-in-out forwards;
  }
  
  .loading-percent {
    margin-top: 2rem;
    font-size: 18px;
    font-weight: 500;
    color: var(--body-color);
    opacity: 0;
    animation: fadeInPercent 0.5s ease-in-out 1s forwards;
  }
  
  @keyframes drawText {
    0% {
      stroke-dashoffset: 1000;
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      stroke-dashoffset: 0;
      opacity: 1;
      fill: var(--primary-color);
    }
  }
  
  @keyframes fadeInPercent {
    to {
      opacity: 1;
    }
  }
  
  /* Global Page Enhancement Styles */
  .page-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .page-content.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Reading Progress Bar */
  .reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #FF6B47);
    z-index: 9999;
    transition: width 0.3s ease;
  }
  
  /* Global Image Loading Animation */
  .img-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .img-animate.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Smooth Scroll */
  html {
    scroll-behavior: smooth;
  }
  
  /* Global Responsive Enhancements */
  @media (max-width: 768px) {
    .preloader-text-svg {
      width: 250px;
    }
    
    .svg-text {
      font-size: 80px;
    }
    
    .loading-percent {
      font-size: 16px;
    }
  }
  
  @media (max-width: 480px) {
    .preloader-text-svg {
      width: 200px;
    }
    
    .svg-text {
      font-size: 60px;
    }
  }
  
  /* Global Performance Optimizations */
  * {
    box-sizing: border-box;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* Global Accessibility Improvements */
  @media (prefers-reduced-motion: reduce) {
    .svg-text {
      animation: none;
      opacity: 1;
      stroke-dashoffset: 0;
      fill: var(--primary-color);
    }
    
    .loading-percent {
      animation: none;
      opacity: 1;
    }
    
    .page-content,
    .img-animate {
      transition: none;
    }
  }
</style>

<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Artix Digital Agency",
  "url": "https://artix.co.in",
  "logo": "https://artix.co.in/assets/img/logo/dark-logo.png",
  "description": "Professional digital agency offering branding, creative designs, web development, and mobile app development services.",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "IN",
    "addressLocality": "India"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+91 81065 34629",
    "contactType": "customer service",
    "email": "<EMAIL>"
  },
  "sameAs": [
    "https://facebook.com/artixdigital",
    "https://twitter.com/artixdigital",
    "https://linkedin.com/company/artixdigital"
  ]
}
</script>

</head>

<body class="dark">
    <!-- Enhanced Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#FF4A23" />
                <stop offset="50%" stop-color="#FF6B47" />
                <stop offset="100%" stop-color="#FF4A23" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle">Artix</text>
          </svg>
        </div>
        <div class="loading-percent" id="loadingPercent">0%</div>
      </div>
    </div>
    <!-- End Enhanced Preloader -->
    
    <!-- Dynamic Header Placeholder -->
    <div id="header"></div>
    
    <!-- Page Content -->
    <main class="page-content">
<!-- Start Main Content -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <div class="container">
      <div class="ak-center">
        <div class="error-page-container">
          <div class="number-stroke">
            <span class="number">4</span>
            <span class="exclamation">
              <svg
                width="131"
                height="191"
                viewBox="0 0 131 191"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <mask
                  id="path-1-outside-1_0_1"
                  maskUnits="userSpaceOnUse"
                  x="51.3477"
                  y="49.693"
                  width="24"
                  height="97"
                  fill="black"
                >
                  <rect
                    fill="white"
                    x="51.3477"
                    y="49.693"
                    width="24"
                    height="97"
                  />
                  <path
                    d="M72.6678 51.4704L68.8282 117.116H57.3092L53.4696 51.4704H72.6678ZM63.1926 144.551C60.3438 144.551 57.9285 143.56 55.9468 141.578C53.965 139.597 52.9741 137.181 52.9741 134.332C52.9741 131.525 53.965 129.13 55.9468 127.149C57.9285 125.167 60.3438 124.176 63.1926 124.176C66 124.176 68.3947 125.167 70.3764 127.149C72.3582 129.13 73.349 131.525 73.349 134.332C73.349 137.181 72.3582 139.597 70.3764 141.578C68.3947 143.56 66 144.551 63.1926 144.551Z"
                  />
                </mask>
                <path
                  d="M72.6678 51.4704L73.9043 51.5428L73.981 50.2318H72.6678V51.4704ZM68.8282 117.116V118.355H69.9964L70.0646 117.188L68.8282 117.116ZM57.3092 117.116L56.0727 117.188L56.141 118.355H57.3092V117.116ZM53.4696 51.4704V50.2318H52.1564L52.2331 51.5428L53.4696 51.4704ZM55.9468 141.578L55.071 142.454L55.9468 141.578ZM55.9468 127.149L55.071 126.273L55.9468 127.149ZM70.3764 141.578L69.5006 140.702L70.3764 141.578ZM71.4313 51.3981L67.5917 117.044L70.0646 117.188L73.9043 51.5428L71.4313 51.3981ZM68.8282 115.877H57.3092V118.355H68.8282V115.877ZM58.5457 117.044L54.7061 51.3981L52.2331 51.5428L56.0727 117.188L58.5457 117.044ZM53.4696 52.709H72.6678V50.2318H53.4696V52.709ZM63.1926 143.312C60.6662 143.312 58.5678 142.448 56.8226 140.702L55.071 142.454C57.2892 144.672 60.0214 145.789 63.1926 145.789V143.312ZM56.8226 140.702C55.0774 138.957 54.2127 136.859 54.2127 134.332H51.7355C51.7355 137.504 52.8527 140.236 55.071 142.454L56.8226 140.702ZM54.2127 134.332C54.2127 131.853 55.0736 129.773 56.8226 128.024L55.071 126.273C52.8565 128.487 51.7355 131.197 51.7355 134.332H54.2127ZM56.8226 128.024C58.5678 126.279 60.6662 125.415 63.1926 125.415V122.937C60.0214 122.937 57.2892 124.055 55.071 126.273L56.8226 128.024ZM63.1926 125.415C65.6722 125.415 67.7516 126.275 69.5006 128.024L71.2522 126.273C69.0377 124.058 66.3279 122.937 63.1926 122.937V125.415ZM69.5006 128.024C71.2496 129.773 72.1104 131.853 72.1104 134.332H74.5876C74.5876 131.197 73.4667 128.487 71.2522 126.273L69.5006 128.024ZM72.1104 134.332C72.1104 136.859 71.2458 138.957 69.5006 140.702L71.2522 142.454C73.4705 140.236 74.5876 137.504 74.5876 134.332H72.1104ZM69.5006 140.702C67.7516 142.451 65.6722 143.312 63.1926 143.312V145.789C66.3279 145.789 69.0377 144.669 71.2522 142.454L69.5006 140.702Z"
                  fill="#FF4A23"
                  mask="url(#path-1-outside-1_0_1)"
                />
                <path
                  d="M35.4936 182.641L35.4959 182.642C43.7873 187.751 53.8141 190.29 65.5471 190.29C77.2796 190.29 87.2662 187.751 95.4762 182.641C103.741 177.548 110.417 170.619 115.503 161.866C120.66 153.047 124.398 142.95 126.721 131.581C129.042 120.224 130.202 108.292 130.202 95.785C130.202 83.2783 129.042 71.3461 126.721 59.9898C124.398 48.5388 120.661 38.3596 115.504 29.4592C110.42 20.5412 103.744 13.5283 95.4776 8.43414C87.2674 3.24091 77.2802 0.660854 65.5471 0.660854C53.8126 0.660854 43.7847 3.24149 35.4928 8.43413C27.3088 13.5284 20.6339 20.541 15.4674 29.4574L15.4653 29.4611C10.3915 38.361 6.69621 48.5396 4.37292 59.99C2.05198 71.3462 0.892421 83.2783 0.892421 95.785C0.892421 108.292 2.05201 120.224 4.37303 131.581C6.69649 142.949 10.3921 153.046 15.4665 161.863L15.47 161.869C20.6373 170.62 27.312 177.548 35.4936 182.641Z"
                  stroke="#FF4A23"
                  stroke-width="1.2386"
                />
              </svg>
            </span>
            <span class="number">4</span>
          </div>
          <h2 class="error-message">Page Not Found!</h2>
          <p class="error-description">
            We're sorry, but the page you're looking for doesn't exist.
          </p>
          <div class="more-btn style3 home-back-btn">
            <span class="text-1"> Back To Home</span>

            <span class="svg-icon">
              <svg
                width="22"
                height="23"
                viewBox="0 0 22 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_2476343543_323)">
                  <path
                    d="M21.1224 11.0496L11.4311 1.32042C11.2082 1.09665 10.8454 1.09627 10.6212 1.31956C10.3971 1.54286 10.396 1.9057 10.6189 2.12947L19.3352 10.8799L1.28014 10.8609C0.963872 10.8605 0.706807 11.1166 0.705908 11.4329C0.705458 11.5912 0.769035 11.7342 0.872408 11.838C0.975782 11.9418 1.11875 12.0061 1.27689 12.0061L19.3322 12.0253L10.566 20.7573C10.3418 20.9806 10.3408 21.3434 10.5637 21.5672C10.7866 21.791 11.1494 21.7914 11.3736 21.5681L21.1201 11.8595C21.3443 11.6362 21.3453 11.2734 21.1224 11.0496Z"
                    fill="#FF4A23"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_2476343543_323">
                    <rect
                      width="21.0576"
                      height="22"
                      fill="white"
                      transform="translate(0.46875 0.432617)"
                    />
                  </clipPath>
                </defs>
              </svg>

              <svg
                width="22"
                height="23"
                viewBox="0 0 22 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_2476_3233423)">
                  <path
                    d="M21.1224 11.0496L11.4311 1.32042C11.2082 1.09665 10.8454 1.09627 10.6212 1.31956C10.3971 1.54286 10.396 1.9057 10.6189 2.12947L19.3352 10.8799L1.28014 10.8609C0.963872 10.8605 0.706807 11.1166 0.705908 11.4329C0.705458 11.5912 0.769035 11.7342 0.872408 11.838C0.975782 11.9418 1.11875 12.0061 1.27689 12.0061L19.3322 12.0253L10.566 20.7573C10.3418 20.9806 10.3408 21.3434 10.5637 21.5672C10.7866 21.791 11.1494 21.7914 11.3736 21.5681L21.1201 11.8595C21.3443 11.6362 21.3453 11.2734 21.1224 11.0496Z"
                    fill="#FF4A23"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_2476_3233423">
                    <rect
                      width="21.0576"
                      height="22"
                      fill="white"
                      transform="translate(0.46875 0.432617)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </span>
          </div>
        </div>
      </div>
    </div>
    <!-- End Main Content -->

    

    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <span class="ak-scrollup">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z"
          fill="currentColor"
        />
      </svg>
    </span>
    <!-- Start Video Popup -->
    <div class="ak-video-popup">
      <div class="ak-video-popup-overlay"></div>
      <div class="ak-video-popup-content">
        <div class="ak-video-popup-layer"></div>
        <div class="ak-video-popup-container">
          <div class="ak-video-popup-align">
            <div class="embed-responsive embed-responsive-16by9">
              <iframe class="embed-responsive-item" src="about:blank"></iframe>
            </div>
          </div>
          <div class="ak-video-popup-close"></div>
        </div>
      </div>
    </div>
    <!-- End Video Popup -->

    <!-- Script -->
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer"></div>
    
    <!-- Mode Toggle -->
    <div class="mode-toggle">
        <div class="setting-mode">
            <button id="open">
                <i class="flaticon-sun"></i>
            </button>
            <button id="clecel">
                <i class="flaticon-close-button-1"></i>
            </button>
        </div>
        <div class="mode-btn js-mode-type">
            <button data-mode="light" class="mode-light">
                <i class="flaticon-sun"></i>
            </button>
            <button data-mode="dark" class="active mode-dark">
                <i class="flaticon-night-mode"></i>
            </button>
        </div>
    </div>
    
    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
        </svg>
    </span>
    
    <!-- Video Popup -->
    <div class="ak-video-popup">
        <div class="ak-video-popup-overlay"></div>
        <div class="ak-video-popup-content">
            <div class="ak-video-popup-layer"></div>
            <div class="ak-video-popup-container">
                <div class="ak-video-popup-align">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="ak-video-popup-close"></div>
            </div>
        </div>
    </div>
    
<!-- Master JavaScript Template for All Pages (Except index.html) -->
<!-- This provides consistent functionality across all pages -->

<!-- Core Scripts -->
<script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
<script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
<script src="assets/js/plugins/swiper.min.js"></script>
<script src="assets/js/plugins/gsap.js"></script>
<script src="assets/js/plugins/lenis.min.js"></script>
<script src="assets/js/plugins/splittext.js"></script>
<script src="assets/js/plugins/scrolltigger.js"></script>
<script src="assets/js/plugins/scrolltoplugins.js"></script>
<script src="assets/js/main.js"></script>
<script src="assets/js/component-loader.js"></script>

<!-- Global Enhanced Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Global Variables
  const pageType = 'error';
  const pageName = '404 Error';
  
  // Enhanced Preloader with Progress Animation
  const preloader = document.getElementById('preloader');
  const loadingPercent = document.getElementById('loadingPercent');
  const svgText = document.getElementById('svgText');
  
  if (preloader && loadingPercent) {
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(progressInterval);
        
        // Hide preloader after animation completes
        setTimeout(() => {
          preloader.style.opacity = '0';
          setTimeout(() => {
            preloader.style.display = 'none';
            // Initialize page content animations
            initPageAnimations();
          }, 500);
        }, 500);
      }
      loadingPercent.textContent = Math.floor(progress) + '%';
    }, 100);
  }
  
  // Global Page Animations
  function initPageAnimations() {
    // Fade in main content
    const pageContent = document.querySelector('.page-content') || document.querySelector('main');
    if (pageContent) {
      pageContent.classList.add('loaded');
    }
    
    // Initialize image animations
    initImageAnimations();
    
    // Initialize reading progress
    initReadingProgress();
    
    // Initialize smooth scrolling
    initSmoothScrolling();
  }
  
  // Global Image Loading Animations
  function initImageAnimations() {
    const images = document.querySelectorAll('img:not(.no-animate)');
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('img-animate');
          setTimeout(() => {
            entry.target.classList.add('loaded');
          }, 100);
          imageObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });
    
    images.forEach(img => {
      if (img.complete && img.naturalHeight !== 0) {
        img.classList.add('img-animate', 'loaded');
      } else {
        img.addEventListener('load', () => {
          imageObserver.observe(img);
        });
        // Fallback for images that might already be cached
        if (img.complete) {
          imageObserver.observe(img);
        }
      }
    });
  }
  
  // Global Reading Progress Bar
  function initReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.body.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      progressBar.style.width = Math.min(scrollPercent, 100) + '%';
    });
  }
  
  // Global Smooth Scrolling for Internal Links
  function initSmoothScrolling() {
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }
  
  // Global Accordion Enhancement (if present)
  const accordionItems = document.querySelectorAll('.ak-accordion-item');
  if (accordionItems.length > 0) {
    accordionItems.forEach(item => {
      const titleContent = item.querySelector('.ak-accordion-title-content');
      const tab = item.querySelector('.ak-accordion-tab');
      
      if (titleContent && tab) {
        titleContent.addEventListener('click', () => {
          // Close all other accordion items
          accordionItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.querySelector('.ak-accordion-title-content').classList.remove('active');
              const otherTab = otherItem.querySelector('.ak-accordion-tab');
              if (otherTab) otherTab.style.maxHeight = null;
            }
          });
          
          // Toggle current item
          titleContent.classList.toggle('active');
          
          if (titleContent.classList.contains('active')) {
            tab.style.maxHeight = tab.scrollHeight + 'px';
          } else {
            tab.style.maxHeight = null;
          }
        });
      }
    });
  }
  
  // Global Performance Tracking
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      console.log(`${pageName} page loaded in ${Math.round(loadTime)}ms`);
      
      // Send analytics if available
      if (typeof gtag !== 'undefined') {
        gtag('event', 'page_load_time', {
          event_category: 'Performance',
          event_label: pageName,
          value: Math.round(loadTime)
        });
      }
    });
  }
  
  // Global Error Handling
  window.addEventListener('error', (e) => {
    console.error('Global error on', pageName, ':', e.error);
    
    // Send error to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: e.error.toString(),
        fatal: false
      });
    }
  });
});

// Component Loading Event Listeners
document.addEventListener('componentLoaded', function(event) {
  const { elementId } = event.detail;
  
  if (elementId === 'header') {
    // Initialize header-specific functionality
    console.log('Header loaded and ready');
    
    // Add active class to current page navigation
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.ak-nav_list a');
    
    navLinks.forEach(link => {
      const linkHref = link.getAttribute('href');
      if (linkHref === currentPage || 
          (currentPage === 'index.html' && linkHref === '/') ||
          (currentPage === '' && linkHref === 'index.html')) {
        link.parentElement.classList.add('current-menu-item');
      }
    });
    
    // Initialize mobile menu if present
    const mobileMenuToggle = document.querySelector('.offcanvaopen-btn');
    if (mobileMenuToggle) {
      console.log('Mobile menu initialized');
    }
  }
  
  if (elementId === 'footer') {
    // Initialize footer-specific functionality
    console.log('Footer loaded and ready');
    
    // Update copyright year
    const copyrightYear = document.querySelector('.copyright-year');
    if (copyrightYear) {
      copyrightYear.textContent = new Date().getFullYear();
    }
  }
});

// Error Handling for Component Loading
document.addEventListener('componentLoadError', function(event) {
  console.warn('Component failed to load:', event.detail);
  
  // Provide fallback functionality
  if (event.detail.elementId === 'header') {
    const headerElement = document.getElementById('header');
    if (headerElement) {
      headerElement.innerHTML = `
        <header style="padding: 20px; background: var(--body-bg-color); border-bottom: 1px solid var(--border-color);">
          <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
            <a href="index.html" style="font-size: 24px; font-weight: bold; color: var(--primary-color); text-decoration: none;">Artix</a>
            <nav>
              <a href="index.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Home</a>
              <a href="about.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">About</a>
              <a href="services.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Services</a>
              <a href="portfolio.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Portfolio</a>
              <a href="contact.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Contact</a>
            </nav>
          </div>
        </header>
      `;
    }
  }
  
  if (event.detail.elementId === 'footer') {
    const footerElement = document.getElementById('footer');
    if (footerElement) {
      footerElement.innerHTML = `
        <footer style="padding: 40px 20px; background: var(--body-bg-color); border-top: 1px solid var(--border-color); text-align: center;">
          <div style="max-width: 1200px; margin: 0 auto;">
            <p style="color: var(--body-color); margin: 0;">&copy; ${new Date().getFullYear()} Artix Digital Agency. All rights reserved.</p>
          </div>
        </footer>
      `;
    }
  }
});

// Global Utility Functions
window.ArtixGlobal = {
  // Smooth scroll to element
  scrollTo: function(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },
  
  // Show loading state
  showLoading: function(element) {
    if (element) {
      element.style.opacity = '0.5';
      element.style.pointerEvents = 'none';
    }
  },
  
  // Hide loading state
  hideLoading: function(element) {
    if (element) {
      element.style.opacity = '1';
      element.style.pointerEvents = 'auto';
    }
  },
  
  // Get page info
  getPageInfo: function() {
    return {
      type: 'error',
      name: '404 Error',
      url: window.location.href,
      title: document.title
    };
  }
};
</script>

</body>
</html>