<!DOCTYPE html>
<html class="no-js" lang="en">
  <head>
    <!-- Meta Tags -->
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="author" content="Artix Digital Agency" />
    
    <!-- Favicon Icon -->
    <link rel="icon" href="assets/img/logo/favicon.png" />
    
    <!-- Site Title -->
    <title>Component Test - Dynamic Loading | Artix</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
    <link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
    <link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/css/style.css" />
    
    <!-- Component Loading Styles -->
    <style>
      .component-loading {
        opacity: 0.5;
        transition: opacity 0.3s ease;
      }
      
      .component-loaded {
        opacity: 1;
      }
      
      .test-content {
        padding: 100px 0;
        text-align: center;
        min-height: 60vh;
      }
      
      .test-section {
        background: var(--body-bg-color);
        border: 2px dashed var(--border-color);
        border-radius: 10px;
        padding: 40px;
        margin: 40px 0;
      }
      
      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      .status-loading {
        background: #ffa500;
        animation: pulse 1s infinite;
      }
      
      .status-success {
        background: #28a745;
      }
      
      .status-error {
        background: #dc3545;
      }
      
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
      
      .component-info {
        background: var(--section-bg-color);
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: left;
      }
      
      .component-info h4 {
        color: var(--primary-color);
        margin-bottom: 10px;
      }
      
      .component-info p {
        margin: 5px 0;
        font-size: 14px;
        color: var(--body-color);
      }
    </style>
  </head>

  <body class="dark">
    <!-- Dynamic Preloader Placeholder -->
    <div id="preloader-container" class="component-loading"></div>
    
    <!-- Dynamic Header Placeholder -->
    <div id="header-container" class="component-loading"></div>
    
    <!-- Test Content -->
    <main class="test-content">
      <div class="container">
        <div class="test-section">
          <h1>🧪 Component Loading Test</h1>
          <p class="lead">Testing dynamic loading of header, footer, and preloader components</p>
          
          <div class="row">
            <div class="col-md-4">
              <div class="component-info">
                <h4>📋 Preloader Component</h4>
                <p><span id="preloader-status" class="status-indicator status-loading"></span><span id="preloader-text">Loading...</span></p>
                <p><strong>Source:</strong> /Volumes/EXT SSD/.../preloader.html</p>
                <p><strong>Expected:</strong> Artix SVG animation</p>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="component-info">
                <h4>🧭 Header Component</h4>
                <p><span id="header-status" class="status-indicator status-loading"></span><span id="header-text">Loading...</span></p>
                <p><strong>Source:</strong> /Volumes/EXT SSD/.../header.html</p>
                <p><strong>Expected:</strong> Full navigation + offcanvas</p>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="component-info">
                <h4>🦶 Footer Component</h4>
                <p><span id="footer-status" class="status-indicator status-loading"></span><span id="footer-text">Loading...</span></p>
                <p><strong>Source:</strong> /Volumes/EXT SSD/.../footer.html</p>
                <p><strong>Expected:</strong> CTA + contact info + links</p>
              </div>
            </div>
          </div>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <h2>Test Results</h2>
          <div id="test-results">
            <p>⏳ Running component loading tests...</p>
          </div>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <h3>Sample Content</h3>
          <p>This is sample content to test the layout with loaded components.</p>
          <p>The header should appear above this content, and the footer should appear below.</p>
          
          <div class="ak-height-50 ak-height-lg-30"></div>
          
          <div class="btn-wrapper">
            <button onclick="reloadComponents()" class="btn btn-primary">
              🔄 Reload Components
            </button>
            <button onclick="showComponentSources()" class="btn btn-secondary">
              📄 View Sources
            </button>
          </div>
        </div>
      </div>
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer-container" class="component-loading"></div>

    <!-- Mode Toggle -->
    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
      </svg>
    </span>

    <!-- Scripts -->
    <script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
    <script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
    <script src="assets/js/plugins/swiper.min.js"></script>
    <script src="assets/js/plugins/gsap.js"></script>
    <script src="assets/js/plugins/splittext.js"></script>
    <script src="assets/js/plugins/scrolltigger.js"></script>
    <script src="assets/js/plugins/scrolltoplugins.js"></script>
    <script src="assets/js/plugins/lenis.min.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- Component Loading Script -->
    <script>
      // Component loading configuration
      const componentConfig = {
        preloader: {
          container: 'preloader-container',
          source: '/Volumes/EXT SSD/All Files/ARTIX WORK/ARTIX/2025/ARTIX WEBSITE/cretio-digital-agency-html-template-2025-04-07-10-07-24-utc/Cretio - Digital Agency HTML Template/preloader.html',
          fallbackSource: 'preloader.html',
          statusElement: 'preloader-status',
          textElement: 'preloader-text'
        },
        header: {
          container: 'header-container',
          source: '/Volumes/EXT SSD/All Files/ARTIX WORK/ARTIX/2025/ARTIX WEBSITE/cretio-digital-agency-html-template-2025-04-07-10-07-24-utc/Cretio - Digital Agency HTML Template/header.html',
          fallbackSource: 'header.html',
          statusElement: 'header-status',
          textElement: 'header-text'
        },
        footer: {
          container: 'footer-container',
          source: '/Volumes/EXT SSD/All Files/ARTIX WORK/ARTIX/2025/ARTIX WEBSITE/cretio-digital-agency-html-template-2025-04-07-10-07-24-utc/Cretio - Digital Agency HTML Template/footer.html',
          fallbackSource: 'footer.html',
          statusElement: 'footer-status',
          textElement: 'footer-text'
        }
      };

      // Component loading results
      let loadingResults = {
        preloader: { loaded: false, error: null, loadTime: 0 },
        header: { loaded: false, error: null, loadTime: 0 },
        footer: { loaded: false, error: null, loadTime: 0 }
      };

      // Load a single component
      async function loadComponent(componentName, config) {
        const startTime = performance.now();
        const container = document.getElementById(config.container);
        const statusElement = document.getElementById(config.statusElement);
        const textElement = document.getElementById(config.textElement);

        try {
          // Update status
          if (textElement) textElement.textContent = 'Loading...';

          // Try primary source first
          let response;
          try {
            response = await fetch(config.source);
          } catch (primaryError) {
            console.warn(`Primary source failed for ${componentName}, trying fallback:`, primaryError);
            response = await fetch(config.fallbackSource);
          }

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const html = await response.text();

          if (!html.trim()) {
            throw new Error('Empty response received');
          }

          // Load the component
          container.innerHTML = html;
          container.classList.remove('component-loading');
          container.classList.add('component-loaded');

          // Update status
          if (statusElement) {
            statusElement.className = 'status-indicator status-success';
          }
          if (textElement) {
            textElement.textContent = 'Loaded successfully';
          }

          const loadTime = performance.now() - startTime;
          loadingResults[componentName] = {
            loaded: true,
            error: null,
            loadTime: Math.round(loadTime)
          };

          console.log(`✅ ${componentName} loaded successfully in ${Math.round(loadTime)}ms`);

          // Trigger custom event
          document.dispatchEvent(new CustomEvent('componentLoaded', {
            detail: { componentName, loadTime }
          }));

          return true;

        } catch (error) {
          console.error(`❌ Failed to load ${componentName}:`, error);

          // Update status
          if (statusElement) {
            statusElement.className = 'status-indicator status-error';
          }
          if (textElement) {
            textElement.textContent = `Error: ${error.message}`;
          }

          const loadTime = performance.now() - startTime;
          loadingResults[componentName] = {
            loaded: false,
            error: error.message,
            loadTime: Math.round(loadTime)
          };

          // Show fallback content
          container.innerHTML = `
            <div style="padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;">
              <h4>⚠️ ${componentName.charAt(0).toUpperCase() + componentName.slice(1)} Loading Failed</h4>
              <p><strong>Error:</strong> ${error.message}</p>
              <p><strong>Source:</strong> ${config.source}</p>
              <button onclick="reloadComponent('${componentName}')" style="background: #721c24; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                🔄 Retry
              </button>
            </div>
          `;

          return false;
        }
      }

      // Load all components
      async function loadAllComponents() {
        console.log('🚀 Starting component loading test...');

        const startTime = performance.now();

        // Load components in parallel
        const promises = Object.entries(componentConfig).map(([name, config]) =>
          loadComponent(name, config)
        );

        const results = await Promise.allSettled(promises);
        const totalTime = performance.now() - startTime;

        // Update test results
        updateTestResults(totalTime);

        // Initialize any loaded components
        initializeLoadedComponents();

        console.log(`🏁 Component loading test completed in ${Math.round(totalTime)}ms`);
      }

      // Update test results display
      function updateTestResults(totalTime) {
        const resultsContainer = document.getElementById('test-results');
        const successCount = Object.values(loadingResults).filter(r => r.loaded).length;
        const totalCount = Object.keys(loadingResults).length;

        let html = `
          <div style="background: ${successCount === totalCount ? '#d4edda' : '#f8d7da'};
                      color: ${successCount === totalCount ? '#155724' : '#721c24'};
                      padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>📊 Test Results Summary</h4>
            <p><strong>Components Loaded:</strong> ${successCount}/${totalCount}</p>
            <p><strong>Total Time:</strong> ${Math.round(totalTime)}ms</p>
            <p><strong>Status:</strong> ${successCount === totalCount ? '✅ All components loaded successfully!' : '⚠️ Some components failed to load'}</p>
          </div>

          <div style="text-align: left;">
        `;

        Object.entries(loadingResults).forEach(([name, result]) => {
          html += `
            <div style="background: var(--section-bg-color); padding: 10px; margin: 5px 0; border-radius: 5px;">
              <strong>${name.charAt(0).toUpperCase() + name.slice(1)}:</strong>
              ${result.loaded ?
                `<span style="color: #28a745;">✅ Loaded (${result.loadTime}ms)</span>` :
                `<span style="color: #dc3545;">❌ Failed - ${result.error}</span>`
              }
            </div>
          `;
        });

        html += '</div>';
        resultsContainer.innerHTML = html;
      }

      // Initialize loaded components
      function initializeLoadedComponents() {
        // Initialize preloader if loaded
        if (loadingResults.preloader.loaded) {
          const preloader = document.getElementById('preloader');
          if (preloader) {
            // Hide preloader after a short delay for testing
            setTimeout(() => {
              preloader.style.opacity = '0';
              setTimeout(() => {
                preloader.style.display = 'none';
              }, 500);
            }, 2000);
          }
        }

        // Initialize header navigation if loaded
        if (loadingResults.header.loaded) {
          // Add current page highlighting
          const navLinks = document.querySelectorAll('.ak-nav_list a');
          navLinks.forEach(link => {
            if (link.getAttribute('href') === 'component-test.html') {
              link.parentElement.classList.add('current-menu-item');
            }
          });
        }
      }

      // Reload a specific component
      window.reloadComponent = function(componentName) {
        const config = componentConfig[componentName];
        if (config) {
          loadComponent(componentName, config);
        }
      };

      // Reload all components
      window.reloadComponents = function() {
        // Reset status indicators
        Object.values(componentConfig).forEach(config => {
          const statusElement = document.getElementById(config.statusElement);
          const textElement = document.getElementById(config.textElement);
          const container = document.getElementById(config.container);

          if (statusElement) statusElement.className = 'status-indicator status-loading';
          if (textElement) textElement.textContent = 'Loading...';
          if (container) {
            container.classList.add('component-loading');
            container.classList.remove('component-loaded');
          }
        });

        // Reset results
        Object.keys(loadingResults).forEach(key => {
          loadingResults[key] = { loaded: false, error: null, loadTime: 0 };
        });

        // Reload all components
        loadAllComponents();
      };

      // Show component sources
      window.showComponentSources = function() {
        const sources = Object.entries(componentConfig).map(([name, config]) =>
          `${name}: ${config.source}`
        ).join('\n');

        alert('Component Sources:\n\n' + sources);
      };

      // Start loading when DOM is ready
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🧪 Component Test Page Loaded');
        console.log('📁 Component Sources:', componentConfig);

        // Start loading components
        loadAllComponents();
      });

      // Listen for component loaded events
      document.addEventListener('componentLoaded', function(event) {
        console.log(`🎉 Component loaded event: ${event.detail.componentName}`);
      });
    </script>
