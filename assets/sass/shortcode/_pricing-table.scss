/*--------------------------------------------------------------
25. Pricing Calculator
----------------------------------------------------------------*/

.package-content {
  display: flex;
  gap: 30px;
  &.style3 {
    gap: 0;
  }
  .package-one,
  .package-two,
  .package-three {
    flex-grow: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    max-width: 440px;
    width: 100%;
    &.style2 {
      transition: all 0.3s ease;
      &.active {
        background-color: rgba($solidblack, 100%);
        cursor: pointer;
        color: $white;
        .common-btn.style-3 {
          background-color: $primary;
          border: 1px solid $black;
          color: $white;
        }
        .package-head-info {
          .title {
            color: $white;
          }
        }
        .package-price-info {
          .price {
            color: $white;
          }
        }
        .package-list {
          li {
            i {
              color: $primary;
            }
          }
        }
      }
    }
    &.style3 {
      border: 1px solid var(--border-color);

      .package-btn-content {
        padding: 20px 20px 13px 20px;
        width: 100%;
        border-top: 1px solid var(--border-color);

        &:hover {
          background-color: $primary;
          .package-btn {
            color: $white;
            &::after {
              background-color: $white;
            }
            .svg-icon {
              path {
                stroke: $white;
              }
              i {
                color: $white;
              }
            }
          }
        }
      }
      .package-btn {
        position: relative;
        display: inline-flex;
        color: var(--black-color);
        font-size: 16px;
        font-weight: 500;
        text-transform: uppercase;
        margin-bottom: 6px;
        flex-shrink: 0;

        .primary-icon-anim {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: 28px;
          height: 24px;
          flex-shrink: 0;
          position: relative;
          overflow: hidden;

          i {
            position: absolute;
            font-size: larger;
            line-height: 30%;
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
            color: $white;
            font-size: 16px;
            &:nth-of-type(1) {
              transform: translate(0, 0);
              opacity: 1;
            }

            &:nth-of-type(2) {
              transform: translate(-40px, 60px);
              opacity: 0;
            }
          }
        }

        &::after {
          content: "";
          position: absolute;
          display: inline;
          width: 0%;
          height: 1px;
          left: 0;
          bottom: 5px;
          opacity: 1;
          transition: all 0.3s;
          background-color: var(--black-color);
        }
        &:hover {
          color: var(--black-color);
          &::after {
            width: 70%;
          }
          .primary-icon-anim {
            i:nth-of-type(1) {
              transform: translate(40px, -60px);
              opacity: 0;
              color: $white;
            }

            i:nth-of-type(2) {
              transform: translate(0, 0);
              opacity: 1;
              color: $white;
            }
          }
        }
      }
    }

    .package-head-info {
      position: relative;
      margin: 60px 0;
      .title {
        font-size: 35px;
        color: var(--black-color);
      }

      .duration {
        font-size: 16px;
      }
    }
    .package-price-info {
      margin: 60px 0 40px 0;
      .price {
        color: var(--black-color);
        font-size: 45px;
        .max-price {
          font-size: 16px;
          font-weight: 400;
          opacity: 0.8;
        }
      }
    }

    .package-list {
      text-align: start;
      max-width: 320px;
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;
      li {
        display: flex;
        gap: 10px;
        i {
          color: $primary;
          width: 26px;
          height: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          background-color: #c1c1c115;
          border-radius: 50%;
        }
      }
    }
    .common-btn {
      margin-bottom: 50px;
    }
  }
}

@media screen and (max-width: 1199px) {
  .package-content {
    .package-one,
    .package-two,
    .package-three {
      &.style2 {
        max-width: 420px;
        width: 100%;
      }
    }
  }
}

@media screen and (max-width: 991px) {
  .package-content {
    flex-direction: column;
    align-items: center;
  }
}
