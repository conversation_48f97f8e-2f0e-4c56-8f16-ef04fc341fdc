/*--------------------------------------------------------------
  12. Button style
----------------------------------------------------------------*/

.btn-wrapper {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .button-container {
    width: 180px;
    height: 50px;
    position: absolute;
    .custom-button {
      width: 180px;
      height: 50px;
      cursor: pointer;
      background: transparent;
      border: 1px solid var(--border-color);
      outline: none;
      transition: 1s ease-in-out;
      border-radius: 50px;
      padding: 5px;

      &:hover {
        transition: 1s ease-in-out;
      }

      span {
        color: var(--black-color);
        font-size: 18px;
        font-weight: 500;
        text-transform: uppercase;
        line-height: 100%;
        margin-top: 3px;
      }

      svg {
        position: absolute;
        left: 0;
        top: 0;
        fill: none;
        stroke: var(--black-color);
        stroke-dasharray: 150 480;
        stroke-dashoffset: 150;
        border-radius: 50px;
        transition: all 1s ease-in-out;
        overflow: hidden;
        &:hover {
          stroke-dashoffset: -480;
        }
      }
    }
  }
}

.btn-8 {
  $btn-color: black;
  $btn-color-dark: black;
  color:$btn-color;

  position: relative;
  overflow: hidden;

  &:before,
  &:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: $btn-color-dark;
    transition: transform 0.3s ease;
  }

  &:before {
    transform: translateY(-100%);
  }
  &:after {
    transform: translateY(100%);
  }

  &:hover {
    color: rgba($btn-color, 75%);

    &:before {
      transform: translateY(-50%);
    }
    &:after {
      transform: translateY(50%);
    }
  }
}

.common-btn {
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  border-radius: 50px;
  padding: 19px 25px;
  color: var(--black-color);
  border: 1px solid var(--border-color);
  transition: color 0.3s ease, background-color 0.3s ease,
    border-color 0.3s ease, transform 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    color: var(--white-color);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.05);

    &::before {
      transform: translateX(0);
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    transform: translateX(-100%);
    transition: transform 0.5s ease;
  }
}
.dark {
  .offcanvaopen-btn {
    background-color: $white;
    i {
      color: $black;
    }
  }
}

.offcanvaopen-btn {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  i {
    line-height: 100%;
    font-size: 21px;
    margin-top: 5px;
    transition: transform 0.4s ease;
    transform-origin: center;
  }
}

.circle-btn {
  width: 165px;
  height: 165px;
  font-size: 18px;
  font-weight: 500;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  text-align: start;
  transition: background-color 0.3s, border-color 0.3s, border-width 0.3s;
  text-transform: uppercase;
  &:hover {
    color: var(--white-color);
    background-color: var(--black-color);
    transition: 0.5s background-color;
    border: 0 solid transparent;
  }
  &.style-1 {
    background-color: var(--white-color);
    &:hover {
      color: var(--white-color);
      background-color: var(--black-color);
    }
  }
  &.style-2 {
    color: $white;
    background-color: $primary;
    &:hover {
      color: var(--white-color);
      background-color: var(--black-color);
    }
  }

  &.style-3 {
    color: $black;
    background-color: $white;
    &:hover {
      color: $white;
      background-color: $black;
    }
  }
  &.style-4 {
    color: $white;
    background-color: $primary;
    &:hover {
      background-color: $white;
      color: $black;
    }
  }
}

.more-btn {
  position: relative;
  display: inline-flex;
  color: var(--black-color);
  font-size: 16px;
  font-weight: 500;
  min-width: 100px;
  text-transform: uppercase;
  margin-bottom: 6px;
  flex-shrink: 0;
  cursor: pointer;
  align-items: center;

  &.style2 {
    border-radius: 50px;
    border: 2px solid var(--border-color);
    display: inline-flex;
    padding: 19px 25px;
    font-style: normal;
    font-weight: 500;
    overflow: hidden;
    .text-1 {
      position: relative;
      transition: all 0.3s;
      transform: scale3d(1, 1, 1) rotate3d(1, 0, 0, 0deg);
    }

    &:hover {
      .text-1 {
        transform: scale3d(1.05, 1.05, 1) translateX(2%)
          rotate3d(1, 0, 1, 10deg);
      }
    }
    &::after {
      display: none;
    }
  }
  &.style3 {
    border: none;
    &::after {
      display: none;
    }
  }
  .svg-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 111;
    overflow: hidden;
    transform: rotate3d(0, 0, 1, -45deg);
    > :first-child {
      transition: all 0.3s;
      transform: translateX(-150%);
    }
    > :last-child {
      transition: all 0.3s;
      transform: translateX(-50%);
    }

    i {
      font-size: 20px;
      color: $primary;
    }
    &.style-three {
      transform: rotate3d(0, 0, 1, 0deg);
    }
  }
  &::after {
    content: "";
    position: absolute;
    display: inline;
    width: 0%;
    height: 1px;
    left: 0;
    bottom: 5px;
    opacity: 1;
    transition: all 0.3s;
    background-color: var(--black-color);
  }
  .morebtn-text {
    font-size: 16px;
    margin-top: 3px;
  }
  .primary-icon-anim {
    i {
      font-size: 17px;
    }
  }
  &:hover {
    .svg-icon {
      &.style-two {
        transform: rotate3d(0, 0, 1, 0deg);
      }
      > :first-child {
        transform: translateX(50%);
      }
      > :last-child {
        transform: translateX(170%) rotateX(50deg);
      }
    }
    color: var(--black-color);
    &::after {
      width: 70%;
    }
    .primary-icon-anim {
      i:nth-of-type(1) {
        transform: translate(40px, -60px);
        opacity: 0;
        color: $primary;
      }

      i:nth-of-type(2) {
        transform: translate(0, 0);
        opacity: 1;
        color: $primary;
      }
    }
  }
  &.arrow-left-style {
    &::after {
      right: 0;
      left: auto;
    }
    .svg-icon {
      transform: rotate3d(0, 0, 1, 0deg);
      > :first-child {
        transition: all 0.3s;
        transform: translateX(210%);
      }
      > :last-child {
        transition: all 0.3s;
        transform: translateX(-50%);
      }
    }
    &:hover {
      .svg-icon {
        > :first-child {
          transform: translateX(50%);
        }
        > :last-child {
          transform: translateX(-210%) rotateX(50deg);
        }
      }
    }
  }
}

.primary-icon-anim {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  i {
    position: absolute;
    font-size: larger;
    line-height: 30%;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    color: $primary;
    &:nth-of-type(1) {
      transform: translate(0, 0);
      opacity: 1;
    }

    &:nth-of-type(2) {
      transform: translate(-40px, 60px);
      opacity: 0;
    }
  }
}

.arrow-circle-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
  cursor: pointer;
  svg {
    transition: margin 0.3s ease-in-out;
    path {
      fill: var(--black-color);
    }
  }
  &.prev {
    &:hover {
      border: 1px solid var(--primary-color);
      svg {
        margin-right: 10px;
        path {
          fill: var(--primary-color);
        }
      }
    }
  }
  &.next {
    &:hover {
      border: 1px solid var(--primary-color);
      svg {
        margin-left: 10px;
        path {
          fill: var(--primary-color);
        }
      }
    }
  }
}
@media screen and (min-width: 1399px) {
  .btn-flip-text {
    perspective: 1000px;
    text-decoration: none;

    &:hover {
      span {
        transform: rotateX(90deg) translateY(-12px);
      }
    }

    span {
      position: relative;
      display: inline-block;
      padding: 0;
      transition: transform 0.3s ease, color 0.3s ease;
      transform-origin: 50% 0;
      transform-style: preserve-3d;

      &::before {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        height: 100%;
        content: attr(data-text);
        transition: color 0.3s ease;
        transform: rotateX(-90deg);
        transform-origin: 50% 0;
        text-align: center;
      }
    }
  }
}

/*End Dark mode btn */
@media screen and (max-width: 1199px) {
  .circle-btn {
    width: 150px;
    height: 150px;
  }
}

@media screen and (max-width: 767px) {
  .circle-btn {
    width: 130px;
    height: 130px;
    font-size: 16px;
  }
}
