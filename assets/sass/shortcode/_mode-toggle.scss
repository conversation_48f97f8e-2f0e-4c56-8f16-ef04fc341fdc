/*--------------------------------------------------------------
21. DarkMode Theme Btn
----------------------------------------------------------------*/

.mode-toggle {
  position: relative;
  font-size: 18px;
  cursor: pointer;
  z-index: 10000;
  .setting-mode {
    position: fixed;
    right: 0px;
    top: 40%;
    -webkit-transition: all 0.2s;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: all 0.2s;
    cursor: pointer;
    mix-blend-mode: exclusion;
    button {
      border: none;
      line-height: normal;
      background-color: #2b2b2f;
      padding: 7px;
      border-radius: 5px;
      i {
        display: inline-block;
        line-height: normal;
        color: $white;
        margin-top: 3px;
        font-size: 13px;
      }
    }
    #clecel {
      display: none;
    }
  }
  .mode-btn {
    position: fixed;
    right: -50px;
    top: 40%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    mix-blend-mode: exclusion;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 50px;

    button {
      border: none;
      background-color: #2b2b2f;
      color: #8f8f8fee;
      line-height: normal;
      i {
        line-height: normal;
      }
      &.mode-light {
        padding: 8px 8px 4px 8px;
      }
      &.mode-dark {
        padding: 4px 8px 8px 8px;
      }
    }
    .active {
      color: #fff;
    }
  }
}

.offcanvas-logo-content {
  .dark-logo {
    display: block;
  }
  .white-logo {
    display: none;
  }
}

.dark {
  .circle-btn {
    &.style-4 {
      background-color: $white;
      color: $black;
      &:hover {
        background-color: $black;
        color: $white;
      }
    }
  }
  .offcanvas-logo-content {
    .dark-logo {
      display: none;
    }
    .white-logo {
      display: block;
    }
  }
  .ak-site_header {
    .ak-main_header {
      .ak-main_header_in {
        .ak-main-header-left {
          .dark-logo {
            display: none;
          }
          .white-logo {
            display: block;
          }
        }
      }
    }
  }
  .ak-site_header.style-2 {
    &.type2.ak-gescout_sticky {
      .ak-main-header-left {
        .ak-site_branding.dark-logo {
          display: none;
        }
        .ak-site_branding.white-logo {
          display: block;
        }
      }
    }
  }
}

.ak-site_header {
  .ak-main_header {
    .ak-main_header_in {
      .ak-main-header-left {
        .dark-logo {
          display: block;
        }
        .white-logo {
          display: none;
        }
      }
    }
  }
}

.ak-site_header.style-2 {
  &.type2 {
    .ak-main-header-center {
      .ak-nav {
        .ak-nav_list.typle2 {
          ul {
            @media screen and (min-width: 1200px) {
              a {
                color: var(--white-color);
                &:hover {
                  color: var(--primary-color);
                }
              }
            }
          }
        }
      }
    }
    .ak-main-header-right {
      .btn-wrapper {
        .button-container {
          span {
            color: $white;
          }
        }
      }
    }
    .ak-main-header-left {
      .ak-site_branding.dark-logo {
        display: none;
      }
      .ak-site_branding.white-logo {
        display: block;
      }
    }
  }
  &.type2.ak-gescout_sticky {
    .ak-main-header-left {
      .ak-site_branding.dark-logo {
        display: block;
      }
      .ak-site_branding.white-logo {
        display: none;
      }
    }
    .ak-main-header-right {
      .btn-wrapper {
        .button-container {
          span {
            color: var(--black-color);
          }
        }
      }
    }
    .ak-nav {
      .ak-nav_list {
        &.typle2 {
          a {
            color: var(--black-color);
            &:hover {
              color: var(--primary-color);
            }
          }
        }
      }
    }
  }
}
