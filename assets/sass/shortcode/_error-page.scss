/*--------------------------------------------------------------
32. Error Page Content
----------------------------------------------------------------*/

.error-page-container {
  margin-top: 50px;
  max-width: 550px;
  width: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  justify-content: center;
  .number-stroke {
    display: flex;
    align-items: center;
    gap: 10px;

    .number {
      font-size: clamp(150px, 25vw, 247px);
      font-weight: 700;
      color: transparent;
      line-height: normal;
      background-color: transparent;
      background-image: none;
      text-shadow: -1px -1px 0 var(--black-color), 0px -1px 0 var(--black-color),
        1px -1px 0 var(--black-color), 1px 0px 0 var(--black-color),
        1px 1px 0 var(--black-color), 0px 1px 0 var(--black-color),
        -1px 1px 0 var(--black-color), -1px 0 0 var(--black-color);
      color: var(--white-color);
      word-spacing: -32px;
    }
    .exclamation {
      margin-bottom: 50px;
    }
  }

  .error-message {
    font-size: 55px;
    line-height: 120%;
    margin-bottom: 10px;
  }

  .error-description {
    margin-bottom: 50px;
    color: var(--body-color);
  }
  .home-back-btn {
    border: 1px solid var(--border-color) !important;
    border-radius: 50px !important;
    padding: 15px 25px 15px 28px;
    .text-1 {
      margin-top: 5px;
    }
  }
}
.upcomming-soon-container {
  min-height: 100vh;
  padding: 15px;
  .upcomming-soon-wapper {
    max-width: 930px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4vw;
    @media (max-width: 768px) {
      gap: 50px;
    }
    .upcomming-soon-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
      .time-counter {
        .funfact-content {
          @media (min-width: 992px) {
            margin-left: 70px;
          }
          & > * {
            width: 22.5%;
          }
          @media (max-width: 575px) {
            width: 100%;
            & > * {
              width: 50% !important;
              text-align: center;
            }
            .funfact.style1 {
              .funfact-card {
                width: 175px !important;
                height: 175px !important;
              }
            }
          }
          .funfact.style1 {
            .funfact-card {
              width: 246px;
              height: 246px;

              border-radius: 50%;
              .funfact-number {
                font-size: 70px;
              }
            }
          }
        }
      }
      .upcomming-soon-info {
        max-width: 500px;
        width: 100%;
        text-align: center;
        .upcomming-soon-title {
          font-size: clamp(50px, 10vw, 100px);
          font-style: italic;
          font-weight: 400;
          line-height: normal;
          font-family: "Instrument Serif";

          .highlight {
            color: $primary;
          }
        }
        .upcomming-soon-desp {
          font-size: 18px;
          line-height: 165%;
          text-align: center;
        }
      }
    }
    .social-wapper {
      display: flex;
      flex-direction: column;
      gap: 30px;
      .social-content {
        display: flex;
        justify-content: space-between;
        gap: 30px;
        flex-wrap: wrap;
        .social-title {
          max-width: 500px;
          width: 100%;
          font-size: 28px;
          line-height: 130%;
          font-weight: 600;
          .heghtlight {
            font-family: var(--secondary-font-family);
            color: var(--primary-color);
            font-style: italic;
            font-weight: 400;
            position: relative;
            &.underline {
              &::after {
                content: "";
                position: absolute;
                bottom: 3px;
                left: 50%;
                transform: translateX(-50%);
                width: 100%;
                height: 1px;
                background-color: var(--primary-color);
              }
            }
          }
        }
      }
      .email-send-form {
        position: relative;
        input {
          width: 100%;
          padding: 20px 30px;
          border-radius: 50px;
          border: 1px solid var(--border-color);
          background-color: var(--white-color);
          transition: all 0.3s ease;
          color: var(--black-color);
        }
        .email-send-btn {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translate(0, -50%);
          background-color: transparent;
          border: 0;
          font-weight: 500;
          line-height: normal;
          text-transform: uppercase;
          button {
            background-color: transparent;
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .error-page-container {
    .number-stroke {
      .exclamation {
        svg {
          width: 100px;
          height: 100px;
        }
      }
    }
  }
}
