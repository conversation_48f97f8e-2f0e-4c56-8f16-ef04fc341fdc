/*--------------------------------------------------------------
30. Progress Bar
----------------------------------------------------------------*/

.ak-skill-box {
  margin-bottom: 50px;
  &:last-child {
    margin-bottom: 0;
  }
  &.type-2 {
    .ak-skill-bar {
      background-color: $black;
      .ak-skill-fill {
        background-color: $white;
      }
    }
    .ak-skill-text {
      .ak-skill-title {
        color: $white;
      }
      .ak-skill-per {
        color: $white;
      }
    }
  }
  .ak-skill-text {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;

    .ak-skill-title {
      font-size: 16px;
      color: var(--heading-color);
      text-transform: uppercase;
    }

    .ak-skill-per {
      font-size: 16px;
      color: var(--black-color);
      font-weight: 400;
    }
  }

  .ak-skill-bar {
    background-color: var(--border-color);
    height: 8px;
    width: 100%;
    overflow: hidden;

    .ak-skill-fill {
      background-color: var(--black-color);
      height: 100%;
      width: 0;
    }
  }
}
.progress-goal {
  display: flex;
  & > * {
    width: 50%;
  }
}
.goal-circle-container {
  display: flex;
  gap: 40px;
  align-items: flex-end;
  justify-content: center;
  .goal-circle {
    position: relative;
    width: 236px;
    .goal-circle-overlay {
      width: 100%;
      height: 100%;
    }
    img {
      position: absolute;
      left: 50%;
      top: 0;
      transform: rotateY(0deg);
      transition: transform 0.5s ease, right 0.5s ease;
      transform-origin: center center;
    }
  }
}

@keyframes zigzag-animation {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}

@media screen and (max-width: 991px) {
  .progress-goal {
    flex-direction: column;
    gap: 50px;
    & > * {
      width: 100%;
    }
    .goal-shape {
      display: flex;
    }
  }
}
