/*--------------------------------------------------------------
  23. Accordion
----------------------------------------------------------------*/

.ak-accordion-tab {
  display: none;
}
.ak-accordion {
  display: flex;
  flex-direction: column;
  gap: 45px;
  .ak-accordion-item:first-child {
    .ak-accordion-tab {
      display: block;
    }
  }
}
.ak-accordion-item {
  .ak-accordion-title-content {
    height: 53px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    &:after {
      content: "";
      position: absolute;
      width: 100%;
      height: 1px;
      background-color: var(--border-color);
      bottom: -5px;
    }

    .ak-accordion-title {
      font-size: 22px;
      font-weight: 400;
      color: var(--black-color);
      width: 100%;
    }
    span {
      align-self: flex-start;
      transform: rotate(180deg);
    }
    &.active {
      .accordion-icon {
        border: 1px solid $primary;
        svg {
          path {
            fill: $primary;
          }
        }
      }
      span {
        transform: rotate(0deg);
      }
    }
    .accordion-icon {
      border: 1px solid var(--black-color);
      border-radius: 50%;
      width: 26px;
      height: 26px;
      text-align: center;
      svg {
        path {
          fill: var(--black-color);
        }
      }
    }
  }
  .ak-accordion-tab {
    padding-top: 30px;
  }
  &.style-2 {
    padding: 23px;
    .ak-accordion-title-content {
      height: auto;
      &:after {
        display: none;
      }
    }
  }
  &.style-3 {
    .ak-accordion-title-content {
      &:after {
        background-color: #063232;
      }
      .ak-accordion-title {
        color: $white;
      }
    }
    .ak-accordion-tab {
      padding-bottom: 50px;
    }
  }
}

.faq-wapper {
  display: flex;
  gap: 72px;
  align-items: center;
  .faq-img-content {
    max-width: 870px;
    width: 100%;
  }
  .faq-accordion {
    max-width: 677px;
    width: 100%;
  }
}

@media screen and (max-width: 1199px) {
  .faq-wapper {
    flex-direction: column;
    gap: 50px;
    align-items: center;
    .faq-img-content {
      .image-hov-one {
        max-height: 400px;
        height: 100%;
      }
    }
  }
}
