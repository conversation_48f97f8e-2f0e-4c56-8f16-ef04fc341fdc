/*--------------------------------------------------------------
28. Newsletter
----------------------------------------------------------------*/
.dark {
  .newsletter-content {
    .newsletter-form {
      .newsletter-input {
        background-image: linear-gradient(black, black),
          linear-gradient(
            to right,
            $primary 0%,
            $primary 25%,
            $black 50%,
            $primary 75%,
            $primary 100%
          );
        &:focus {
          background-image: linear-gradient(black, black),
            linear-gradient(
              to right,
              $primary 0%,
              $black 25%,
              $primary 50%,
              $black 75%,
              $primary 100%
            );
        }
      }
    }
  }
}

.newsletter-content {
  display: flex;
  flex-direction: column;
  align-items: center;

  &.style-2 {
    flex-direction: row;
    justify-content: space-between;
    .newsletter-title-content {
      .newsletter-title {
        max-width: 685px;
        width: 100%;
        text-align: start;
        margin: 0;
        .highlight {
          &.text-underlines::after {
            bottom: 6px;
          }
        }
      }
    }
    .newsletter-title {
      font-size: 55px;
    }
    .newsletter-form {
      top: auto;
    }
  }
  .newsletter-title {
    max-width: 941px;
    width: 100%;
    font-size: 70px;
    font-weight: 600;
    line-height: 105%;
    text-align: center;
    margin: 24px 0;
    .highlight {
      font-style: italic;
      font-family: var(--secondary-font-family);
      font-weight: 400;
      color: $primary;
      position: relative;

      &.text-underlines {
        &::after {
          content: "";
          position: absolute;
          left: 0%;
          bottom: 22px;
          width: 94%;
          height: 1px;
          background-color: $primary;
          transform: scaleX(0);
          transform-origin: center;
          transition: transform 1s ease-in-out, opacity 1s ease-in-out;
          opacity: 0;
        }

        &.active {
          &::after {
            transition-delay: 1.2s;
            transform: scaleX(1);
            opacity: 1;
          }
        }
      }
    }
  }

  .newsletter-form {
    max-width: 520px;
    width: 100%;
    height: 70px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    top: 28px;

    .newsletter-input {
      width: 100%;
      height: 100%;
      padding: 20px 30px;
      outline: none;
      background: transparent;
      font-size: 18px;
      border: 1px solid transparent;
      border-radius: 50px;
      overflow: hidden;
      transition: background-image 0.5s ease;
      &.style-2 {
        transition: border 0.5s ease;
        background-image: none;
        border: 1px solid var(--border-color);
        &:focus {
          border-color: var(--balck-color);
        }
      }
      background-image: linear-gradient(white, white),
        linear-gradient(
          to right,
          $primary 0%,
          $primary 25%,
          $black 50%,
          $primary 75%,
          $primary 100%
        );
      background-origin: border-box;
      background-clip: padding-box, border-box;

      &:focus {
        background-image: linear-gradient(white, white),
          linear-gradient(
            to right,
            $primary 0%,
            $black 25%,
            $primary 50%,
            $black 75%,
            $primary 100%
          );
      }
    }

    .newsletter-btn {
      position: absolute;
      right: 32px;
      border: none;
      background-color: transparent;
      font-weight: 500;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      .newbtn-text {
        font-size: 18px;
        margin-top: 3px;
      }
      &:hover .primary-icon-anim {
        i:nth-of-type(1) {
          transform: translate(40px, -60px);
          opacity: 0;
        }

        i:nth-of-type(2) {
          transform: translate(0, 0);
          opacity: 1;
        }
      }
    }
  }
}

.newsletter-wapper {
  &.active {
    .cta-form-border {
      .b-top-left,
      .b-top-right,
      .b-bottom-right,
      .b-bottom-left {
        transition: all 0.5s ease-in-out;
        opacity: 1;
      }
    }
    .newsletter-content {
      .newsletter-anim {
        .newsletter-title {
          .highlight {
            &.text-underlines {
              &::after {
                transform: scaleX(1);
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .newsletter-content {
    .newsletter-title {
      font-size: 60px;
      max-width: 800px;
    }

    .newsletter-form {
      width: 480px;
    }
  }
}

@media screen and (max-width: 992px) {
  .newsletter-content {
    &.style-2 {
      gap: 30px;
      flex-wrap: wrap;
      .newsletter-title {
        font-size: 50px;
      }
    }
    .newsletter-title {
      font-size: 50px;
      max-width: 700px;
    }

    .newsletter-form {
      width: 400px;
      height: 65px;

      .newsletter-input {
        font-size: 16px;
        padding: 15px 25px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .newsletter-content {
    &.style-2 {
      .newsletter-title {
        font-size: 36px;
      }
    }
    .newsletter-title {
      font-size: 42px;
      max-width: 600px;
    }

    .newsletter-form {
      width: 90%;
      height: 60px;

      .newsletter-input {
        font-size: 14px;
        padding: 12px 20px;
      }
    }
  }
}
@media screen and (max-width: 576px) {
  .newsletter-content {
    .newsletter-title {
      font-size: 36px;
      max-width: 500px;
      line-height: 108%;
      .highlight {
        &.text-underlines::after {
          bottom: 10px;
        }
      }
    }
    .newsletter-form {
      width: 100%;
      height: 55px;

      .newsletter-input {
        font-size: 16px;
        padding: 10px 15px;
      }
    }

    .newsletter-btn {
      right: 10px !important;
      .newbtn-text {
        font-size: 16px !important;
      }
    }
  }
}
