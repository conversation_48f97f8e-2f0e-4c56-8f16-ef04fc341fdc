/*--------------------------------------------------------------
3. Preloader
----------------------------------------------------------------*/
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--white-color);
  z-index: 99999999;
  overflow: hidden;
  transform: translateY(0);

  .txt-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
    color: var(--black-color);
    // font-family: "Instrument, sans-serif";
    font-size: 160px;
    font-weight: 700;

    .preloader-text {
      text-align: center;
      max-width: 800px;
      width: 100%;

      .preloader-text-svg {
        width: 80%;
        max-width: 900px;
        height: auto;

        .svg-text {
          fill: none;
          stroke: var(--black-color);
          stroke-width: 1;
          font-size: 200px;
          opacity: 0;
          transform: translate(150px, 200px);
          font-family: "Instrument", sans-serif !important;
          background: var(--white-color);
        }
      }
    }

    .loading-percent {
      margin-top: 1rem;
      font-size: 18px;
    }
  }
}

@media screen and (max-width: 991px) {
  .preloader {
    .txt-loading {
      font-size: 42px;

      .loading-percent {
        font-size: 16px;
      }
    }
  }
}
