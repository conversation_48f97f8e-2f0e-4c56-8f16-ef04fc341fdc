/* 
|......................................................................
| Template Name: Cretio
| Author: Thememarch
| Version: 1.0
|--------------------------------------------------------------------------
|--------------------------------------------------------------------------
| TABLE OF CONTENTS:
|--------------------------------------------------------------------------
| 1. Basic Color Variable
| 2. Typography
| 3. Preloader
| 4. <PERSON>cing
| 5. General
| 6. Slider
| 7. <PERSON> Popup
| 8. <PERSON><PERSON>
| 9. <PERSON><PERSON>
| 10. Pagination
| 11. Section Heading Title
| 12. Button style
| 13. Hero
| 14. Service
| 15. Moving Text
| 16. About Content
| 17. Counter Funfact
| 18. Feature Content
| 19. Testmonial
| 20. Team
| 21. DarkMode Theme Btn
| 22. Blog
| 23. Accordion
| 24. Cta
| 25. Pricing Calculator
| 26. Portfolio
| 27. Awards
| 28. Newsletter
| 29. News Card
| 30. Progress Bar
| 31. Contact Form
| 32. Error Page
 ................................................................*/
/*--------------------------------------------------------------
1. Basic Color Variable
----------------------------------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Epilogue:ital,wght@0,100..900;1,100..900&family=Instrument+Serif:ital@0;1&display=swap");
:root {
  --body-font-family: "Epilogue", sans-serif;
  --heading-font-family: "Epilogue", sans-serif;
  --secondary-font-family: "Instrument Serif";
  --body-color: #353535;
  --body-bg-color: #ffffff;
  --heading-color: #101010;
  --border-color: #ececec;
  --white-color: #ffffff;
  --black-color: #101010;
  --primary-color: #ff4a23;
  --secondary-color: #353535;
  --gray-bg: #f1f1f1;
}

.dark {
  --body-bg-color: #101010;
  --heading-color: #ffffff;
  --body-color: #c1c1c1;
  --gray-bg: #000000;
  --black-color: #ffffff;
  --white-color: #101010;
  --secondary-color: #c1c1c1;
  --border-color: #353535;
}

/*--------------------------------------------------------------
2. Typography
----------------------------------------------------------------*/
html,
body {
  color: var(--body-color);
  font-family: var(--body-font-family);
  background-color: var(--body-bg-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 165%;
  overflow-x: hidden;
  scroll-behavior: auto;
  padding-right: 0 !important;
  transition: color 0.5s, background-color 0.5s;
}

html.lenis,
html.lenis body {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto !important;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-smooth iframe {
  pointer-events: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  clear: both;
  color: var(--heading-color);
  font-family: var(--heading-font-family);
  padding: 0px;
  margin: 0px;
  line-height: normal;
  font-style: normal;
}

h1 {
  font-size: 100px;
  font-weight: 700;
}

h2 {
  font-size: 55px;
  font-weight: 700;
}

h3 {
  font-size: 45px;
  font-weight: 600;
}

h4 {
  font-size: 35px;
  font-weight: 600;
}

h5 {
  font-size: 28px;
  font-weight: 500;
}

h6 {
  font-size: 22px;
  font-weight: 500;
}

p {
  margin-bottom: 0;
}

ul {
  margin: 0 0 25px 0;
  padding-left: 20px;
  list-style: square outside none;
}

ol {
  padding-left: 20px;
  margin-bottom: 25px;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

blockquote {
  margin: 0 15px;
  font-style: italic;
  font-size: 20px;
  line-height: 1.6em;
  margin: 0;
}

address {
  margin: 0 0 15px;
}

img {
  border: 0;
  max-width: 100%;
  height: auto;
}

a {
  font-size: 18px;
  color: inherit;
  text-decoration: none;
}

button {
  color: inherit;
  transition: all 0.3s ease;
}

a:hover {
  text-decoration: none;
  color: var(--body-color);
}

table {
  width: 100%;
  margin-bottom: 25px;
}
table th {
  font-weight: 600;
  color: var(--body-color);
}
table td,
table th {
  border-top: 1px solid #ffffff;
  padding: 11px 10px;
}

dl {
  margin-bottom: 25px;
}
dl dt {
  font-weight: 600;
}

b,
strong {
  font-weight: bold;
}

pre {
  color: var(--body-color);
  border: 1px solid #ffffff;
  font-size: 18px;
  padding: 25px;
  border-radius: 5px;
}

kbd {
  font-size: 100%;
  background-color: var(--body-color);
  border-radius: 5px;
}

/*--------------------------------------------------------------
3. Preloader
----------------------------------------------------------------*/
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--white-color);
  z-index: 99999999;
  overflow: hidden;
  transform: translateY(0);
}
.preloader .txt-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  color: var(--black-color);
  font-size: 160px;
  font-weight: 700;
}
.preloader .txt-loading .preloader-text {
  text-align: center;
  max-width: 800px;
  width: 100%;
}
.preloader .txt-loading .preloader-text .preloader-text-svg {
  width: 80%;
  max-width: 900px;
  height: auto;
}
.preloader .txt-loading .preloader-text .preloader-text-svg .svg-text {
  fill: none;
  stroke: var(--black-color);
  stroke-width: 1;
  font-size: 200px;
  opacity: 0;
  transform: translate(150px, 200px);
  font-family: "Instrument", sans-serif !important;
  background: var(--white-color);
}
.preloader .txt-loading .loading-percent {
  margin-top: 1rem;
  font-size: 18px;
}

@media screen and (max-width: 991px) {
  .preloader .txt-loading {
    font-size: 42px;
  }
  .preloader .txt-loading .loading-percent {
    font-size: 16px;
  }
}
/*--------------------------------------------------------------
  4. Spacing
----------------------------------------------------------------*/
@media screen and (min-width: 992px) {
  .ak-height-0 {
    height: 0px;
  }
  .ak-height-5 {
    height: 5px;
  }
  .ak-height-10 {
    height: 10px;
  }
  .ak-height-15 {
    height: 15px;
  }
  .ak-height-20 {
    height: 20px;
  }
  .ak-height-25 {
    height: 25px;
  }
  .ak-height-30 {
    height: 30px;
  }
  .ak-height-35 {
    height: 35px;
  }
  .ak-height-40 {
    height: 40px;
  }
  .ak-height-45 {
    height: 45px;
  }
  .ak-height-50 {
    height: 50px;
  }
  .ak-height-55 {
    height: 55px;
  }
  .ak-height-60 {
    height: 60px;
  }
  .ak-height-65 {
    height: 65px;
  }
  .ak-height-70 {
    height: 70px;
  }
  .ak-height-75 {
    height: 75px;
  }
  .ak-height-80 {
    height: 80px;
  }
  .ak-height-85 {
    height: 85px;
  }
  .ak-height-90 {
    height: 90px;
  }
  .ak-height-95 {
    height: 95px;
  }
  .ak-height-100 {
    height: 100px;
  }
  .ak-height-105 {
    height: 105px;
  }
  .ak-height-110 {
    height: 110px;
  }
  .ak-height-115 {
    height: 115px;
  }
  .ak-height-120 {
    height: 120px;
  }
  .ak-height-125 {
    height: 125px;
  }
  .ak-height-130 {
    height: 130px;
  }
  .ak-height-135 {
    height: 135px;
  }
  .ak-height-140 {
    height: 140px;
  }
  .ak-height-145 {
    height: 145px;
  }
  .ak-height-150 {
    height: 150px;
  }
  .ak-height-155 {
    height: 155px;
  }
  .ak-height-160 {
    height: 160px;
  }
  .ak-height-165 {
    height: 165px;
  }
  .ak-height-170 {
    height: 170px;
  }
  .ak-height-175 {
    height: 175px;
  }
  .ak-height-180 {
    height: 180px;
  }
  .ak-height-185 {
    height: 185px;
  }
  .ak-height-190 {
    height: 190px;
  }
  .ak-height-195 {
    height: 195px;
  }
  .ak-height-200 {
    height: 200px;
  }
  .ak-height-205 {
    height: 205px;
  }
  .ak-height-210 {
    height: 210px;
  }
  .ak-height-219 {
    height: 219px;
  }
}
@media screen and (max-width: 991px) {
  .ak-height-lg-0 {
    height: 0px;
  }
  .ak-height-lg-5 {
    height: 5px;
  }
  .ak-height-lg-10 {
    height: 10px;
  }
  .ak-height-lg-15 {
    height: 15px;
  }
  .ak-height-lg-20 {
    height: 20px;
  }
  .ak-height-lg-25 {
    height: 25px;
  }
  .ak-height-lg-30 {
    height: 30px;
  }
  .ak-height-lg-35 {
    height: 35px;
  }
  .ak-height-lg-40 {
    height: 40px;
  }
  .ak-height-lg-45 {
    height: 45px;
  }
  .ak-height-lg-50 {
    height: 50px;
  }
  .ak-height-lg-55 {
    height: 55px;
  }
  .ak-height-lg-60 {
    height: 60px;
  }
  .ak-height-lg-65 {
    height: 65px;
  }
  .ak-height-lg-70 {
    height: 70px;
  }
  .ak-height-lg-75 {
    height: 75px;
  }
  .ak-height-lg-80 {
    height: 80px;
  }
  .ak-height-lg-85 {
    height: 85px;
  }
  .ak-height-lg-90 {
    height: 90px;
  }
  .ak-height-lg-95 {
    height: 95px;
  }
  .ak-height-lg-100 {
    height: 100px;
  }
  .ak-height-lg-105 {
    height: 105px;
  }
  .ak-height-lg-110 {
    height: 110px;
  }
  .ak-height-lg-115 {
    height: 115px;
  }
  .ak-height-lg-120 {
    height: 120px;
  }
  .ak-height-lg-125 {
    height: 125px;
  }
  .ak-height-lg-130 {
    height: 130px;
  }
  .ak-height-lg-135 {
    height: 135px;
  }
  .ak-height-lg-140 {
    height: 140px;
  }
  .ak-height-lg-145 {
    height: 145px;
  }
  .ak-height-lg-150 {
    height: 150px;
  }
  .ak-height-lg-155 {
    height: 155px;
  }
  .ak-height-lg-160 {
    height: 160px;
  }
  .ak-height-lg-165 {
    height: 165px;
  }
  .ak-height-lg-170 {
    height: 170px;
  }
  .ak-height-lg-175 {
    height: 175px;
  }
  .ak-height-lg-180 {
    height: 180px;
  }
  .ak-height-lg-185 {
    height: 185px;
  }
  .ak-height-lg-190 {
    height: 190px;
  }
  .ak-height-lg-195 {
    height: 195px;
  }
  .ak-height-lg-200 {
    height: 200px;
  }
  .ak-height-lg-205 {
    height: 205px;
  }
  .ak-height-lg-210 {
    height: 210px;
  }
  .ak-height-lg-219 {
    height: 219px;
  }
}
/*--------------------------------------------------------------
  5. General
----------------------------------------------------------------*/
.ak-light {
  font-weight: 300;
}

.ak-normal {
  font-weight: 400;
}

.ak-medium {
  font-weight: 500;
}

.ak-semi-bold {
  font-weight: 600;
}

.ak-bold {
  font-weight: 700;
}

.ak-extra-bold {
  font-weight: 800;
}

.ak-black {
  font-weight: 900;
}

.ak-radius-3 {
  border-radius: 3px;
}

.ak-radius-5 {
  border-radius: 5px;
}

.ak-radius-7 {
  border-radius: 7px;
}

.ak-radius-10 {
  border-radius: 10px;
}

.ak-radius-15 {
  border-radius: 15px;
}

.ak-line-height-1 {
  line-height: 1.2em;
}

.ak-line-height-2 {
  line-height: 1.25em;
}

.ak-line-height-3 {
  line-height: 1.3em;
}

.ak-line-height-4 {
  line-height: 1.4em;
}

.ak-line-height-5 {
  line-height: 1.5em;
}

.ak-line-height-6 {
  line-height: 1.6em;
}

.ak-line-height-7 {
  line-height: 1.7em;
}

.ak-line-height-39 {
  line-height: 39px;
}

.ak-line-height-54 {
  line-height: 54px;
}

.ak-line-height-85 {
  line-height: 85px;
}

.ak-line-height-100 {
  line-height: 100%;
}

.ak-font-14 {
  font-size: 14px;
}

.ak-font-16 {
  font-size: 16px;
}

.ak-font-18 {
  font-size: 18px;
}

.ak-font-20 {
  font-size: 20px;
}

.ak-font-22 {
  font-size: 22px;
}

.ak-font-24 {
  font-size: 24px;
}

.ak-font-26 {
  font-size: 26px;
}

.ak-font-28 {
  font-size: 28px;
}

.ak-font-30 {
  font-size: 30px;
}

.ak-font-38 {
  font-size: 38px;
}

.ak-font-42 {
  font-size: 42px;
}

.ak-font-50 {
  font-size: 50px;
}

.ak-font-62 {
  font-size: 62px;
}

.ak-font-italic {
  font-style: italic;
}

.ak-mp0 {
  list-style: none;
  margin: 0;
  padding: 0;
}

.ak-m0 {
  margin: 0;
}

.ak-mt100 {
  margin-top: 100px;
}

.ak-bg {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.ak-vertical-middle {
  display: flex;
  align-items: center;
  min-height: 100%;
}

.ak-vertical-middle-in {
  flex: none;
  width: 100%;
}

.ak-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ak-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ak-primary-font {
  font-family: var(--heading-font-family);
}

.ak-secondary-font {
  font-family: var(--secondary-font-family);
}

.dark .ak-black-color,
.dark .ak-black-color-hover:hover {
  color: #ffffff !important;
}

.ak-black-color,
.ak-black-color-hover:hover {
  color: #101010 !important;
}

.ak-white-color,
.ak-white-color-hover:hover {
  color: #ffffff;
}

.ak-primary-color,
.ak-primary-color-hover:hover {
  color: #ff4a23;
}

.ak-body-color,
.ak-body-color-hover:hover {
  color: var(--body-color);
}

.ak-body-bg,
.ak-body-bg-hover:hover {
  background-color: var(--white-color) !important;
}

.ak-white-bg-1,
.ak-white-bg-1-hover:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.ak-white-bg,
.ak-white-bg-hover:hover {
  background-color: #ffffff;
}

.ak-primary-bg-1,
.ak-primary-bg-hover-1:hover {
  background-color: rgba(255, 74, 35, 0.7);
}

.ak-primary-bg,
.ak-primary-bg-hover:hover {
  background-color: #ff4a23;
}

.ak-black-bg,
.ak-black-bg-hover:hover {
  background-color: #101010;
}

.ak-solidblack-bg,
.ak-solidblack-bg-hover:hover {
  background-color: #000000;
}

.ak-gray-bg,
.ak-gray-bg-hover:hover {
  background-color: var(--gray-bg);
}

.ak-gradient-bg-1 {
  background: linear-gradient(267.18deg, #161616 0%, #080808 100%);
}

.dot-text {
  position: relative;
  display: inline-block;
  content: "";
  width: 10px;
  height: 10px;
  flex-shrink: 0;
  background-color: #ff4a23;
  margin-bottom: 3px;
  margin-right: 5px;
}

@media screen and (min-width: 992px) {
  .ak-w-20 {
    width: 20%;
  }
  .ak-w-30 {
    width: 30%;
  }
  .ak-w-40 {
    width: 40%;
  }
  .ak-w-50 {
    width: 50%;
  }
  .ak-w-60 {
    width: 60%;
  }
  .ak-w-70 {
    width: 70% !important;
  }
  .ak-w-80 {
    width: 80%;
  }
}
@media screen and (max-width: 991px) {
  .ak-left-space-30 {
    padding-left: 0px;
  }
  .ak-font-14-sm {
    font-size: 14px;
  }
  .ak-font-16-sm {
    font-size: 16px;
  }
  .ak-font-18-sm {
    font-size: 18px;
  }
  .ak-font-20-sm {
    font-size: 20px;
  }
  .ak-font-22-sm {
    font-size: 22px;
  }
  .ak-font-24-sm {
    font-size: 24px;
  }
  .ak-font-26-sm {
    font-size: 26px;
  }
  .ak-font-28-sm {
    font-size: 28px;
  }
  .ak-font-42-sm {
    font-size: 42px;
  }
  .ak-font-36-sm {
    font-size: 36px;
  }
  .ak-btn-group > *:not(:last-child) {
    margin-right: 10px;
  }
  .flex-column-reverse-lg {
    flex-direction: column-reverse;
  }
  .ak-line-height-39 {
    line-height: 100%;
  }
}
.text-decoration-underline {
  text-decoration-thickness: 2px !important;
}

.ak-stroke-text {
  font-size: 50px;
  font-weight: 500;
  line-height: 1.2em;
  color: transparent;
  font-family: var(--heading-font-family);
  -webkit-text-stroke: 1px var(--body-color);
}
.ak-stroke-text.ak-type-1 {
  -webkit-text-stroke: 1.5px var(--body-color);
  text-align: center;
  font-size: 265.289px;
  line-height: 321px;
}

.ak-stroke-number {
  font-size: 55px;
  font-weight: 700;
  color: transparent;
  -webkit-text-stroke: 1px var(--body-color);
}
.ak-stroke-number.ak-type-1 {
  text-align: center;
  font-size: 265.289px;
}
@media screen and (max-width: 991px) {
  .ak-stroke-number.ak-type-1 {
    font-size: 100px;
    line-height: 100px;
  }
}

.ak-fixed-bg {
  background-attachment: fixed;
}

.ak-hover-layer,
.ak-hover-layer1,
.ak-hover-layer3 {
  position: relative;
  transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: perspective(600px) translate3d(0, 0, 0);
}

.ak-tab {
  display: none;
}
.ak-tab.active {
  display: block;
}

.ak-google-map {
  height: 530px;
}
.ak-google-map iframe {
  height: 100%;
  width: 100%;
  display: block;
  border: none;
}
.ak-google-map.ak-type1 {
  height: 100%;
  min-height: 300px;
  width: 50vw;
}

.ak-rotate-img {
  transform: rotate(45deg);
  overflow: hidden;
  border-radius: 7px;
}

.ak-rotate-img-in {
  transform: rotate(-45deg) scale(1.4);
}

.ak-half-screen {
  width: 56vw;
}

.ak-scrollup {
  position: fixed;
  bottom: -60px;
  right: 40px;
  color: #fff;
  padding: 5px;
  height: 50px;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.4s ease;
  z-index: 111111;
  background-color: #000000;
  box-shadow: 0px 1px 5px 1px rgba(16, 16, 16, 0.2);
  border-radius: 50%;
}
.ak-scrollup:hover {
  background-color: #ffffff;
  color: #101010;
}

.ak-scrollup.ak-scrollup-show {
  bottom: 50px;
}

.swiper-slide {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  overflow: hidden;
}
.swiper-slide .ak-entity-img {
  display: none;
}

.primary-color-border {
  width: 100%;
  height: 1px;
  opacity: 0.35;
  background: var(--primary-color);
}

.btn-animation {
  transform-style: preserve-3d;
  transition: font-size 0.5s, background-color 0.3s, transform 0.3s, color 0.3s;
  transform: translate3d(0, 0px, 0px);
}
.btn-animation:hover {
  font-size: 17px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  background-color: rgba(255, 74, 35, 0.7) !important;
  transform: translate3d(0, -8px, 0.01px);
}

.text-letter-spacing-animation {
  letter-spacing: normal;
  transition: letter-spacing 0.3s ease;
  cursor: pointer;
}
.text-letter-spacing-animation:hover {
  letter-spacing: 2px;
  color: var(--white-color);
}

.select-input-customize {
  border-radius: 0;
  background-color: transparent;
  color: #ffffff;
  border: 1px solid #ececec;
}
.select-input-customize:focus {
  border-color: #ff4a23 !important;
  box-shadow: 0 0 0 0.25rem rgba(253, 13, 13, 0.19);
}
.select-input-customize option {
  background-color: var(--black-color);
  color: rgba(255, 255, 255, 0.25);
}
.select-input-customize option:visited {
  background-color: #ff4a23 !important;
  color: #ff4a23 !important;
}

.container-customize {
  max-width: 1720px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.img-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: var(--heading-font-family);
  font-size: 120px;
  font-style: normal;
  font-weight: 900;
  line-height: 125%;
  text-transform: uppercase;
}

.dark .social-icon .icon.style-2.dark-mode {
  border: 1px solid #ffffff !important;
}
.dark .social-icon .icon.style-2.dark-mode i {
  color: #ffffff;
}
.dark .social-icon .icon.style-2.dark-mode:hover {
  background-color: #ffffff;
}
.dark .social-icon .icon.style-2.dark-mode:hover i {
  color: #101010;
}

.social-icon {
  display: flex;
  gap: 15px;
}
.social-icon .icon {
  border: 1px solid #ffffff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease-in-out;
}
.social-icon .icon i {
  color: #ffffff;
  display: contents;
  line-height: 30%;
  font-size: 20px;
}
.social-icon .icon.color-1 {
  background-color: rgba(0, 0, 0, 0.1);
}
.social-icon .icon.color-1 i {
  color: #101010;
}
.social-icon .icon.color-1:hover {
  background-color: #ff4a23;
}
.social-icon .icon.style-2 {
  border: 1px solid #101010;
}
.social-icon .icon.style-2 i {
  color: #101010;
}
.social-icon .icon.style-2:hover {
  background-color: #101010;
}
.social-icon .icon.style-2:hover i {
  color: #ffffff;
}
.social-icon .icon:hover {
  background-color: #ffffff;
  transform: scale(1.2);
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}
.social-icon .icon:hover i {
  color: #101010;
}

.ak-border-width {
  width: 100%;
  height: 1px;
  background: var(--border-color);
}
.ak-border-width.color-black {
  background: rgba(16, 16, 16, 0.2);
}

.image-hov-one {
  position: relative;
  display: block;
  overflow: hidden;
}
.image-hov-one img {
  transition: all 0.3s linear;
  transform: scale(1);
}

.image-hov-one::after {
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  right: 51%;
  top: 0;
  opacity: 1;
  pointer-events: none;
  transition: all 400ms linear;
}

.image-hov-one:hover img {
  transform: scale(1.02);
}
.image-hov-one:hover::after {
  left: 0;
  right: 0;
  opacity: 0;
  transition: all 400ms linear;
}

.split-line {
  display: inline-block !important;
}

.highlight-text {
  font-style: italic;
  color: #ff4a23;
  font-weight: 400;
  font-family: var(--secondary-font-family);
  display: inline;
}

.image-scroll {
  height: 250px;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.image-scroll img {
  position: absolute;
  top: -30%;
  width: 100%;
  height: 130%;
  -o-object-fit: cover;
     object-fit: cover;
}

.img-anim-left-show {
  visibility: hidden;
  overflow: hidden;
}
.img-anim-left-show img {
  -o-object-fit: cover;
     object-fit: cover;
  transform-origin: left;
}

.isotop-item-menu {
  display: flex;
  gap: 40px;
  justify-content: center;
  list-style: none;
  margin-top: 30px;
}
.isotop-item-menu .item-title {
  text-transform: uppercase;
  font-size: 16px;
  cursor: pointer;
  color: var(--black-color);
}
.isotop-item-menu .item-title.is-clicked {
  color: #ff4a23;
}

.isotop-items-portfolio {
  height: auto !important;
  margin-top: 0;
}
.isotop-items-portfolio > * {
  position: relative !important;
  left: auto !important;
  top: auto !important;
}

.upcomming-soon-border {
  position: relative;
  width: 100%;
  max-width: 1015px;
}
.upcomming-soon-border .horizontal {
  width: 250px;
  height: 1px;
  animation: horizontal-grow 0.5s ease-out forwards;
}
.upcomming-soon-border .verticle {
  width: 1px;
  height: 250px;
  animation: vertical-grow 0.5s ease-out forwards;
}
.upcomming-soon-border .b-top-left {
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.upcomming-soon-border .b-top-left.type2 {
  left: 7px;
}
.upcomming-soon-border .b-top-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-top-left .verticle {
  opacity: 0.3;
  margin-top: -10%;
  background: linear-gradient(180deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-top-left::after {
  position: absolute;
  content: "";
  top: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #c1c1c1;
  opacity: 1;
}
.upcomming-soon-border .b-top-right {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s ease;
}
.upcomming-soon-border .b-top-right.type2 {
  right: -14px;
}
.upcomming-soon-border .b-top-right .horizontal {
  margin-right: -10%;
  opacity: 0.3;
  background: linear-gradient(270deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-top-right .verticle {
  margin-top: -10%;
  opacity: 0.3;
  background: linear-gradient(190deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-top-right::after {
  position: absolute;
  content: "";
  top: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #c1c1c1;
  opacity: 1;
}
.upcomming-soon-border .b-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s ease;
}
.upcomming-soon-border .b-bottom-right.type2 {
  right: -14px;
}
.upcomming-soon-border .b-bottom-right .horizontal {
  opacity: 0.3;
  margin-right: -10%;
  background: linear-gradient(270deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-bottom-right .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-bottom-right::after {
  position: absolute;
  content: "";
  bottom: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #c1c1c1;
  opacity: 1;
}
.upcomming-soon-border .b-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}
.upcomming-soon-border .b-bottom-left.type2 {
  left: 7px;
}
.upcomming-soon-border .b-bottom-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-bottom-left .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #c1c1c1 0%, transparent 100%);
}
.upcomming-soon-border .b-bottom-left::after {
  position: absolute;
  content: "";
  bottom: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #c1c1c1;
  opacity: 1;
}
@media (min-width: 1199px) {
  .upcomming-soon-border.hover-animation:hover .b-top-left {
    top: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .upcomming-soon-border.hover-animation:hover .b-top-right {
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .upcomming-soon-border.hover-animation:hover .b-bottom-right {
    bottom: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .upcomming-soon-border.hover-animation:hover .b-bottom-left {
    bottom: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
}

.contact-form-border {
  position: relative;
  width: 100%;
  max-width: 1015px;
}
.contact-form-border .horizontal {
  width: 250px;
  height: 1px;
  animation: horizontal-grow 0.5s ease-out forwards;
}
.contact-form-border .verticle {
  width: 1px;
  height: 250px;
  animation: vertical-grow 0.5s ease-out forwards;
}
.contact-form-border .b-top-left {
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.contact-form-border .b-top-left.type2 {
  left: 7px;
}
.contact-form-border .b-top-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-top-left .verticle {
  opacity: 0.3;
  margin-top: -10%;
  background: linear-gradient(180deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-top-left::after {
  position: absolute;
  content: "";
  top: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.contact-form-border .b-top-right {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s ease;
}
.contact-form-border .b-top-right.type2 {
  right: -14px;
}
.contact-form-border .b-top-right .horizontal {
  margin-right: -10%;
  opacity: 0.3;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-top-right .verticle {
  margin-top: -10%;
  opacity: 0.3;
  background: linear-gradient(190deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-top-right::after {
  position: absolute;
  content: "";
  top: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.contact-form-border .b-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s ease;
}
.contact-form-border .b-bottom-right.type2 {
  right: -14px;
}
.contact-form-border .b-bottom-right .horizontal {
  opacity: 0.3;
  margin-right: -10%;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-bottom-right .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-bottom-right::after {
  position: absolute;
  content: "";
  bottom: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.contact-form-border .b-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}
.contact-form-border .b-bottom-left.type2 {
  left: 7px;
}
.contact-form-border .b-bottom-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-bottom-left .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.contact-form-border .b-bottom-left::after {
  position: absolute;
  content: "";
  bottom: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
@media (min-width: 1199px) {
  .contact-form-border.hover-animation:hover .b-top-left {
    top: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .contact-form-border.hover-animation:hover .b-top-right {
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .contact-form-border.hover-animation:hover .b-bottom-right {
    bottom: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .contact-form-border.hover-animation:hover .b-bottom-left {
    bottom: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
}

.cta-form-border {
  position: relative;
  width: 100%;
  max-width: 1163px;
  padding-right: 28px;
}
.cta-form-border .horizontal {
  width: 250px;
  height: 1px;
  animation: horizontal-grow 0.5s ease-out forwards;
}
.cta-form-border .verticle {
  width: 1px;
  height: 250px;
  animation: vertical-grow 0.5s ease-out forwards;
}
.cta-form-border .b-top-left {
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.cta-form-border .b-top-left.type2 {
  left: 7px;
}
.cta-form-border .b-top-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-top-left .verticle {
  opacity: 0.3;
  margin-top: -10%;
  background: linear-gradient(180deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-top-left::after {
  position: absolute;
  content: "";
  top: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.cta-form-border .b-top-right {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s ease;
}
.cta-form-border .b-top-right.type2 {
  right: -14px;
}
.cta-form-border .b-top-right .horizontal {
  margin-right: -10%;
  opacity: 0.3;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-top-right .verticle {
  margin-top: -10%;
  opacity: 0.3;
  background: linear-gradient(190deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-top-right::after {
  position: absolute;
  content: "";
  top: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.cta-form-border .b-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s ease;
}
.cta-form-border .b-bottom-right.type2 {
  right: -14px;
}
.cta-form-border .b-bottom-right .horizontal {
  opacity: 0.3;
  margin-right: -10%;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-bottom-right .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-bottom-right::after {
  position: absolute;
  content: "";
  bottom: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.cta-form-border .b-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}
.cta-form-border .b-bottom-left.type2 {
  left: 7px;
}
.cta-form-border .b-bottom-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-bottom-left .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.cta-form-border .b-bottom-left::after {
  position: absolute;
  content: "";
  bottom: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
@media (min-width: 1199px) {
  .cta-form-border.hover-animation:hover .b-top-left {
    top: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .cta-form-border.hover-animation:hover .b-top-right {
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .cta-form-border.hover-animation:hover .b-bottom-right {
    bottom: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .cta-form-border.hover-animation:hover .b-bottom-left {
    bottom: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
}
.cta-form-border .b-top-left,
.cta-form-border .b-top-right,
.cta-form-border .b-bottom-right,
.cta-form-border .b-bottom-left {
  transition: all 0.5s ease-in-out;
  opacity: 0;
}

.testmonial-slider-2 {
  position: relative;
  width: 100%;
}
.testmonial-slider-2 .horizontal {
  width: 250px;
  height: 1px;
  animation: horizontal-grow 0.5s ease-out forwards;
}
.testmonial-slider-2 .verticle {
  width: 1px;
  height: 250px;
  animation: vertical-grow 0.5s ease-out forwards;
}
.testmonial-slider-2 .b-top-left {
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.testmonial-slider-2 .b-top-left.type2 {
  left: 7px;
}
.testmonial-slider-2 .b-top-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-top-left .verticle {
  opacity: 0.3;
  margin-top: -10%;
  background: linear-gradient(180deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-top-left::after {
  position: absolute;
  content: "";
  top: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.testmonial-slider-2 .b-top-right {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s ease;
}
.testmonial-slider-2 .b-top-right.type2 {
  right: -14px;
}
.testmonial-slider-2 .b-top-right .horizontal {
  margin-right: -10%;
  opacity: 0.3;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-top-right .verticle {
  margin-top: -10%;
  opacity: 0.3;
  background: linear-gradient(190deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-top-right::after {
  position: absolute;
  content: "";
  top: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.testmonial-slider-2 .b-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s ease;
}
.testmonial-slider-2 .b-bottom-right.type2 {
  right: -14px;
}
.testmonial-slider-2 .b-bottom-right .horizontal {
  opacity: 0.3;
  margin-right: -10%;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-bottom-right .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-bottom-right::after {
  position: absolute;
  content: "";
  bottom: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.testmonial-slider-2 .b-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}
.testmonial-slider-2 .b-bottom-left.type2 {
  left: 7px;
}
.testmonial-slider-2 .b-bottom-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-bottom-left .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.testmonial-slider-2 .b-bottom-left::after {
  position: absolute;
  content: "";
  bottom: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
@media (min-width: 1199px) {
  .testmonial-slider-2.hover-animation:hover .b-top-left {
    top: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .testmonial-slider-2.hover-animation:hover .b-top-right {
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .testmonial-slider-2.hover-animation:hover .b-bottom-right {
    bottom: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .testmonial-slider-2.hover-animation:hover .b-bottom-left {
    bottom: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
}
.testmonial-slider-2 .b-top-left {
  top: 25px;
  left: 27px;
}
.testmonial-slider-2 .b-top-right {
  right: 0px;
  top: 25px;
}
.testmonial-slider-2 .b-bottom-right {
  bottom: 79px;
}
.testmonial-slider-2 .b-bottom-left {
  bottom: 79px;
  left: 27px;
}

.core-features .core-feature-card {
  position: relative;
  width: 100%;
}
.core-features .core-feature-card .horizontal {
  width: 250px;
  height: 1px;
  animation: horizontal-grow 0.5s ease-out forwards;
}
.core-features .core-feature-card .verticle {
  width: 1px;
  height: 250px;
  animation: vertical-grow 0.5s ease-out forwards;
}
.core-features .core-feature-card .b-top-left {
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.core-features .core-feature-card .b-top-left.type2 {
  left: 7px;
}
.core-features .core-feature-card .b-top-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-top-left .verticle {
  opacity: 0.3;
  margin-top: -10%;
  background: linear-gradient(180deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-top-left::after {
  position: absolute;
  content: "";
  top: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.core-features .core-feature-card .b-top-right {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.3s ease;
}
.core-features .core-feature-card .b-top-right.type2 {
  right: -14px;
}
.core-features .core-feature-card .b-top-right .horizontal {
  margin-right: -10%;
  opacity: 0.3;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-top-right .verticle {
  margin-top: -10%;
  opacity: 0.3;
  background: linear-gradient(190deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-top-right::after {
  position: absolute;
  content: "";
  top: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.core-features .core-feature-card .b-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s ease;
}
.core-features .core-feature-card .b-bottom-right.type2 {
  right: -14px;
}
.core-features .core-feature-card .b-bottom-right .horizontal {
  opacity: 0.3;
  margin-right: -10%;
  background: linear-gradient(270deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-bottom-right .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-bottom-right::after {
  position: absolute;
  content: "";
  bottom: -5px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
.core-features .core-feature-card .b-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s ease;
}
.core-features .core-feature-card .b-bottom-left.type2 {
  left: 7px;
}
.core-features .core-feature-card .b-bottom-left .horizontal {
  opacity: 0.3;
  margin-left: -10%;
  background: linear-gradient(90deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-bottom-left .verticle {
  opacity: 0.3;
  margin-bottom: -10%;
  background: linear-gradient(0deg, #ff4a23 0%, transparent 100%);
}
.core-features .core-feature-card .b-bottom-left::after {
  position: absolute;
  content: "";
  bottom: -5px;
  left: -5px;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  opacity: 1;
}
@media (min-width: 1199px) {
  .core-features .core-feature-card.hover-animation:hover .b-top-left {
    top: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .core-features .core-feature-card.hover-animation:hover .b-top-right {
    top: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .core-features .core-feature-card.hover-animation:hover .b-bottom-right {
    bottom: 20px;
    right: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
  .core-features .core-feature-card.hover-animation:hover .b-bottom-left {
    bottom: 20px;
    left: 20px;
    transition: all 0.3s ease;
    opacity: 0.3;
  }
}
.core-features .core-feature-card .b-bottom-right {
  right: -27px;
}
.core-features .core-feature-card .b-top-right {
  right: -27px;
}
.core-features.slider-two .swiper-wrapper {
  height: 405px;
  align-items: center;
  margin-left: 13px;
}
.core-features.slider-two .swiper-wrapper .swiper-slide {
  height: auto;
  overflow: visible;
}

@media screen and (max-width: 1399px) {
  .core-features.slider-two .swiper-wrapper {
    margin-left: 0;
  }
  .core-features.slider-two .swiper-wrapper .b-top-left,
  .core-features.slider-two .swiper-wrapper .b-top-right,
  .core-features.slider-two .swiper-wrapper .b-bottom-right,
  .core-features.slider-two .swiper-wrapper .b-bottom-left {
    display: none !important;
  }
}
@media screen and (max-width: 1199px) {
  .theme-border-wrap .b-top-left {
    left: 1px;
  }
  .theme-border-wrap .b-top-right {
    right: -24px;
  }
  .theme-border-wrap .b-bottom-right {
    right: -24px;
  }
  .theme-border-wrap .b-bottom-left {
    left: 1px;
  }
  .cta-form-border {
    padding-right: 0;
    margin-bottom: 35px;
  }
}
@media screen and (max-width: 991px) {
  .ak-google-map {
    height: 400px;
  }
  .ak-google-map.ak-type1 {
    width: 100%;
    height: 400px;
    border-radius: 15px;
    overflow: hidden;
  }
  .ak-scrollup {
    right: 15px;
  }
  .ak-scrollup.ak-scrollup-show {
    bottom: 50px;
  }
  .theme-border-wrap .b-top-left {
    left: 1px;
  }
  .theme-border-wrap .b-top-right {
    right: -24px;
  }
  .theme-border-wrap .b-bottom-right {
    right: -24px;
  }
  .theme-border-wrap .b-bottom-left {
    left: 1px;
  }
}
@media screen and (max-width: 767px) {
  .ak-cursor-lg,
  .ak-cursor-sm {
    display: none !important;
  }
  .isotop-item-menu {
    gap: 0;
    justify-content: space-between;
    padding: 0;
  }
}
/*--------------------------------------------------------------
  6. Slider
----------------------------------------------------------------*/
.ak-slider {
  position: relative;
  overflow: hidden;
}

.partners-logos-slider {
  position: relative;
  width: 100%;
}
.partners-logos-slider .swiper-slide {
  width: 25%;
}

.partners-swiper-controller {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}

.partners-logs-scrollbar {
  position: relative;
  background: var(--border-color);
  cursor: pointer;
  width: 100%;
  height: 1px;
}
.partners-logs-scrollbar .swiper-scrollbar-drag {
  background: var(--primary-color);
  cursor: pointer;
}

.dark .partners-logs-navigation .partners-logs-button-next.hover-1 svg path, .dark .partners-logs-navigation .hover-1.partners-logs-button-prev svg path, .dark .partners-logs-navigation .partners-logs-button-next.hover-2 svg path, .dark .partners-logs-navigation .hover-2.partners-logs-button-prev svg path {
  fill: #ffffff;
}

.partners-logs-navigation {
  max-width: 125px;
  display: flex;
  justify-content: space-between;
  gap: 25px;
}
.partners-logs-navigation .partners-logs-button-next, .partners-logs-navigation .partners-logs-button-prev {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  align-content: center;
}
.partners-logs-navigation .partners-logs-button-next.hover-1, .partners-logs-navigation .hover-1.partners-logs-button-prev {
  transition: border 0.3s;
}
.partners-logs-navigation .partners-logs-button-next.hover-1 svg, .partners-logs-navigation .hover-1.partners-logs-button-prev svg {
  transition: transform 0.3s;
}
.partners-logs-navigation .partners-logs-button-next.hover-1 svg path, .partners-logs-navigation .hover-1.partners-logs-button-prev svg path {
  transition: borfillder 0.3s;
}
.partners-logs-navigation .partners-logs-button-next.hover-1:hover, .partners-logs-navigation .hover-1.partners-logs-button-prev:hover {
  border: 1px solid var(--primary-color);
}
.partners-logs-navigation .partners-logs-button-next.hover-1:hover svg, .partners-logs-navigation .hover-1.partners-logs-button-prev:hover svg {
  transform: translateX(-5px);
}
.partners-logs-navigation .partners-logs-button-next.hover-1:hover svg path, .partners-logs-navigation .hover-1.partners-logs-button-prev:hover svg path {
  fill: var(--primary-color);
}
.partners-logs-navigation .partners-logs-button-prev.hover-2 {
  transition: border 0.3s;
}
.partners-logs-navigation .partners-logs-button-prev.hover-2 svg {
  transition: transform 0.3s;
}
.partners-logs-navigation .partners-logs-button-prev.hover-2 svg path {
  transition: borfillder 0.3s;
}
.partners-logs-navigation .partners-logs-button-prev.hover-2:hover {
  border: 1px solid var(--primary-color);
}
.partners-logs-navigation .partners-logs-button-prev.hover-2:hover svg {
  transform: translateX(5px);
}
.partners-logs-navigation .partners-logs-button-prev.hover-2:hover svg path {
  fill: var(--primary-color);
}

.client-logo {
  max-width: 245px;
  width: 100%;
  height: 125px;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.client-logo.style2 {
  height: auto;
  border: none;
}
.client-logo .client-info .client-title {
  font-size: 26px;
  font-style: italic;
  font-weight: 400;
  line-height: 100%;
  font-family: var(--secondary-font-family);
}
.client-logo .client-info .client-shot-title {
  font-size: 9.5px;
  font-style: normal;
  font-weight: 300;
  line-height: 165%;
  text-transform: uppercase;
}

@media screen and (max-width: 1299px) {
  .partners-logos-slider .swiper-slide {
    width: 33.333%;
  }
}
@media screen and (max-width: 1199px) {
  .partners-logos-slider .swiper-slide {
    width: 50%;
  }
}
/*--------------------------------------------------------------
7. Video Popup
----------------------------------------------------------------*/
.ak-pd-video .ak-video-open,
.ak-sample-img .ak-video-open {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 68px;
  transition: all 0.3s ease;
  line-height: 48px;
}

.ak-video-popup {
  position: fixed;
  z-index: 1000;
  top: 0;
  width: 100%;
  height: 100%;
  left: -100%;
  transition-delay: 0.3s;
}

.ak-video-popup.active {
  left: 0;
  transition-delay: 0s;
  left: 0;
}

.ak-video-popup-overlay {
  position: absolute;
  left: 0;
  right: 0;
  background: #ffffff;
  transition: all 0.4s ease-out;
  opacity: 0;
}

.ak-video-popup.active .ak-video-popup-overlay {
  opacity: 0.8;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
}

.ak-video-popup-content {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0;
  text-align: center;
  transition: all 0.4s ease-out;
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  padding: 15px;
}

.ak-video-popup.active .ak-video-popup-content {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.ak-video-popup-content:after {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.ak-video-popup-container {
  display: inline-block;
  position: relative;
  text-align: left;
  max-width: 1380px;
  width: 100%;
  vertical-align: middle;
}

.ak-video-popup-container .embed-responsive {
  width: 100%;
}

.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

.embed-responsive-16by9::before {
  display: block;
  content: "";
  padding-top: 56.25%;
}

.embed-responsive iframe,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.ak-video-popup-close {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  cursor: pointer;
  transition: all 0.4s ease-in-out;
}

.ak-video-popup iframe {
  width: 100%;
  height: 100%;
  position: absolute;
}

.ak-video-popup-close:before {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 1px;
  background: #ffffff;
  margin-left: -10px;
  transform: rotate(-45deg);
  transition: all 0.4s ease-in-out;
}

.ak-video-popup-close:after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 1px;
  background: #ffffff;
  margin-left: -10px;
  transform: rotate(45deg);
  transition: all 0.4s ease-in-out;
}

.ak-video-popup-close:hover:before,
.ak-video-popup-close:hover:after {
  background: #101010;
}

.ak-video-popup-layer {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
}

.ak-video-popup-align {
  overflow: hidden;
}

.ak-video-block {
  position: relative;
  height: 800px;
  overflow: hidden;
}
.ak-video-block.about-video-block {
  height: 750px;
}
.ak-video-block .video-img {
  position: absolute;
  width: 100%;
  height: 120%;
  -o-object-fit: cover;
     object-fit: cover;
}
.ak-video-block .video-player-btn {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  color: var(--black-color);
  background-color: var(--white-color);
}

@media screen and (max-width: 1199px) {
  .ak-video-block {
    height: 520px;
  }
  .ak-video-block.about-video-block {
    height: 475px;
  }
}
@media screen and (max-width: 767px) {
  .ak-video-block {
    height: 400px;
  }
  .ak-video-block.about-video-block {
    height: 400px;
  }
}
/*--------------------------------------------------------------
8. Header
----------------------------------------------------------------*/
.ak-site_header {
  position: relative;
  z-index: 100001;
}

.ak-site-branding {
  display: inline-block;
  max-width: 180px;
}

.ak-site_header.ak-style1 .ak-main_header_in,
.ak-site_header.ak-style1 .ak-top_header_in {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
  margin: 0px;
}
.ak-site_header.ak-style1 .ak-main-header-center {
  display: flex;
  align-items: center;
  height: 100%;
}

.ak-site_header_full_width .container {
  max-width: 100%;
  padding: 0 100px;
}

.ak-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.ak-nav .ak-nav_list a {
  color: var(--black-color);
  font-family: var(--body-font-family);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  transition: color 0.3s;
}
.ak-nav .ak-nav_list a:hover {
  color: var(--primary-color);
}
@media screen and (min-width: 1200px) {
  .ak-nav .ak-nav_list.style-2.typle2 a {
    color: #ffffff;
  }
  .ak-nav .ak-nav_list.style-2.typle2 a:hover {
    color: var(--primary-color);
  }
}

.ak-sticky_header {
  position: fixed !important;
  width: 100%;
  z-index: 999;
}

.ak-gescout_sticky {
  position: fixed !important;
  top: -150px;
  transition: top 0.4s ease;
}

.ak-gescout_show {
  top: 0 !important;
  background-color: var(--body-bg-color);
}

.ak-site_branding {
  display: inline-block;
}
.ak-site_branding img {
  max-height: 45px;
}

.dark .btn-close.btn-close-black {
  filter: invert(1) grayscale(100%) brightness(200%);
  border: none;
}

.offcanvas.offcanvas-end.style-1 {
  background-color: var(--body-bg-color);
  width: 500px;
}
.offcanvas.offcanvas-end.style-1 .btn-close {
  opacity: 1;
}
.offcanvas.offcanvas-end.style-1 .btn-close:focus {
  box-shadow: none;
}

.offcanvas-body-coustom-style {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0px 29px;
  width: 100%;
  height: 100%;
}
.offcanvas-body-coustom-style .ak-site_branding {
  margin-bottom: 50px;
}
.offcanvas-body-coustom-style .desp {
  text-align: center;
  font-size: 18px;
  margin-bottom: 50px;
}
.offcanvas-body-coustom-style .offcanvas-footer-contant {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 5px;
}
.offcanvas-body-coustom-style .offcanvas-footer-contant .short-title {
  font-size: 16px;
  line-height: 165%;
}
.offcanvas-body-coustom-style .offcanvas-footer-contant .email {
  font-size: 35px;
  font-weight: 600;
  line-height: 130%;
  color: var(--black-color);
}

@media screen and (min-width: 1200px) {
  .ak-main_header {
    position: relative;
  }
  .ak-main_header .container-fluid {
    padding-right: 40px;
    padding-left: 40px;
  }
  .ak-main_header_center,
  .ak-top_header_center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .ak-site_header.ak-style1 .ak-main_header_center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    max-width: calc(100% - 300px);
  }
  .ak-nav {
    display: flex;
    align-items: center;
    height: 100%;
    line-height: 1.6em;
    font-size: 16px;
  }
  .ak-nav .ak-nav_list {
    display: flex !important;
    flex-wrap: wrap;
    height: inherit;
  }
  .ak-nav .ak-nav_list > li {
    margin-right: 40px;
    height: 80px;
  }
  .ak-nav .ak-nav_list > li:last-child {
    margin-right: 0;
  }
  .ak-nav .ak-nav_list > li > a {
    display: inline-flex;
    position: relative;
    height: inherit;
    align-items: center;
    text-transform: uppercase;
  }
  .ak-nav .ak-nav_list > li > ul {
    left: 0;
    top: calc(100% + 25px);
  }
  .ak-nav .ak-nav_list > li:hover > ul {
    top: calc(100% + 0px);
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
  }
  .ak-nav .ak-nav_list > li.menu-item-has-children > a {
    position: relative;
  }
  .ak-nav .ak-nav_list > li.menu-item-has-children > a:after {
    content: "+";
    margin-left: 5px;
    color: #ff3d24;
    font-size: 30px;
    margin-bottom: 5px;
    font-weight: 200;
  }
  .ak-nav .ak-nav_list > li.menu-item-has-children.cs_changes_color_1 > a {
    position: relative;
  }
  .ak-nav .ak-nav_list > li.menu-item-has-children.cs_changes_color_1 > a::after {
    content: "";
    display: inline-block;
    height: 8px;
    width: 8px;
    border: 2px solid #ffffff;
    transform: rotate(45deg);
    border-left: 0;
    border-top: 0;
    margin-left: 6px;
    position: relative;
    top: -2px;
    border-radius: 0px 0px 2px 0px;
  }
  .ak-nav .ak-nav_list > li.menu-item-has-children > ul > li:last-child {
    border-bottom: none;
  }
  .ak-nav .ak-nav_list li:not(.ak-mega_menu) {
    position: relative;
  }
  .ak-nav .ak-nav_list ul {
    width: 260px;
    background-color: var(--black-color);
    position: absolute;
    box-shadow: 0px 1px 2px 0px rgba(2, 0, 181, 0.1);
    border-top: 2px solid rgba(255, 255, 255, 0.3215686275);
    padding: 35px 15px;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    display: block !important;
    border-radius: 0;
    transition: all 0.1s ease;
  }
  .ak-nav .ak-nav_list ul li a {
    color: var(--white-color);
  }
  .ak-nav .ak-nav_list ul li:hover ul {
    top: 0px;
  }
  .ak-nav .ak-nav_list ul li:hover > ul {
    opacity: 1;
    visibility: visible;
    transition: all 0.4s ease;
  }
  .ak-nav .ak-nav_list ul a {
    display: block;
    line-height: inherit;
    padding: 7px 20px;
    text-transform: uppercase;
  }
  .ak-nav .ak-nav_list ul ul {
    top: 15px;
    left: 100%;
  }
  .ak-munu_toggle,
  .ak-munu_dropdown_toggle {
    display: none;
  }
  .ak-nav_black_section ul {
    position: relative;
    list-style: none;
    line-height: 65px;
    padding: 0px;
  }
  .ak-nav_black_section ul li {
    margin-top: 40px;
    font-size: 55px;
    text-transform: uppercase;
    font-weight: 900;
  }
  .menu-item-has-black-section {
    position: relative;
  }
  .menu-item-has-black-section span {
    cursor: pointer;
  }
  .menu-item-has-black-section > a {
    position: relative;
  }
  .menu-item-has-black-section > ul {
    padding-left: 40px;
    display: none;
    list-style: none;
    line-height: 30px;
  }
  .menu-item-has-black-section > ul li {
    margin-top: 40px;
  }
  .ak-munu_dropdown_toggle_1 {
    position: absolute;
    height: 30px;
    width: 35px;
    right: 20px;
    top: 9px;
  }
  .ak-munu_dropdown_toggle_1:before, .ak-munu_dropdown_toggle_1:after {
    content: "";
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: 2px;
    width: 35px;
    background-color: #101010;
    transition: all 0.3s ease;
  }
  .ak-munu_dropdown_toggle_1:before {
    transform: translate(-50%, -50%) rotate(90deg);
  }
  .ak-munu_dropdown_toggle_1.active:before {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}
@media screen and (max-width: 1199px) {
  .ak-main_header .container {
    max-width: 100%;
  }
  .ak-site_header.ak-style1 .ak-nav {
    display: flex;
  }
  .ak-site_header_full_width .container {
    padding: 0 15px;
  }
  .ak-munu_dropdown_toggle {
    position: absolute;
    height: 30px;
    width: 30px;
    right: 20px;
    top: 5px;
  }
  .ak-munu_dropdown_toggle:before, .ak-munu_dropdown_toggle:after {
    content: "";
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: 2px;
    width: 10px;
    background-color: var(--body-color);
    transition: all 0.3s ease;
  }
  .ak-munu_dropdown_toggle:before {
    transform: translate(-50%, -50%) rotate(90deg);
  }
  .ak-munu_dropdown_toggle.active:before {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  .ak-nav .ak-nav_list {
    position: absolute;
    width: 100vw;
    left: -12px;
    padding: 10px 0;
    display: none;
    top: 0%;
    padding-top: 75px;
    border-top: 1px solid rgba(77, 77, 77, 0.3215686275);
    border-bottom: 1px solid rgba(77, 77, 77, 0.3215686275);
    overflow: auto;
    max-height: calc(100vh - 80px);
    line-height: 1.6em;
    background-color: var(--body-bg-color);
    z-index: -11;
  }
  .ak-nav .ak-nav_list ul {
    padding-left: 15px;
    display: none;
  }
  .ak-nav .ak-nav_list a {
    display: block;
    padding: 8px 20px;
    text-transform: uppercase;
    position: relative;
  }
  .ak-nav .menu-item-has-children {
    position: relative;
  }
  /*Mobile Menu Button*/
  .ak-munu_toggle {
    display: inline-block;
    width: 30px;
    height: 27px;
    cursor: pointer;
    position: absolute;
    top: 27px;
    right: 30px;
  }
  .ak-munu_toggle span,
  .ak-munu_toggle span:before,
  .ak-munu_toggle span:after {
    width: 100%;
    height: 2px;
    background-color: var(--black-color);
    display: block;
  }
  .ak-munu_toggle span {
    margin: 0 auto;
    position: relative;
    top: 12px;
    transition-duration: 0s;
    transition-delay: 0.2s;
  }
  .ak-munu_toggle span:before {
    content: "";
    position: absolute;
    margin-top: -9px;
    transition-property: margin, transform;
    transition-duration: 0.2s;
    transition-delay: 0.2s, 0s;
  }
  .ak-munu_toggle span:after {
    content: "";
    position: absolute;
    margin-top: 9px;
    transition-property: margin, transform;
    transition-duration: 0.2s;
    transition-delay: 0.2s, 0s;
  }
  .ak-site_header.ak-style1 .ak-munu_toggle {
    top: 50%;
    right: 0px;
    margin-top: -13px;
  }
  .ak-toggle_active span {
    background-color: rgba(0, 0, 0, 0);
    transition-delay: 0.2s;
  }
  .ak-toggle_active span:before {
    margin-top: 0;
    transform: rotate(45deg);
    transition-delay: 0s, 0.2s;
  }
  .ak-toggle_active span:after {
    margin-top: 0;
    transform: rotate(-45deg);
    transition-delay: 0s, 0.2s;
  }
  .ak-header_toolbox {
    margin-right: 50px;
  }
  .ak-site_header.ak-style1 .ak-main_header_in {
    height: 80px;
    justify-content: start;
    margin: 0px;
  }
  .ak-site_header .current-menu-item > a:before {
    display: none;
  }
  .ak-site_header.ak-style1 .ak-main_header_center .ak-site_branding {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
  .ak-site_header.ak-style1 {
    top: 0;
    background-color: var(--white-color);
  }
  .dark .ak-site_header.style-2.type2 .ak-main_header_in .ak-main-header-left .dark-logo {
    display: none !important;
  }
  .dark .ak-site_header.style-2.type2 .ak-main_header_in .ak-main-header-left .white-logo {
    display: block !important;
  }
  .ak-site_header.style-2.type2 .ak-main_header_in .ak-main-header-left .dark-logo {
    display: block !important;
  }
  .ak-site_header.style-2.type2 .ak-main_header_in .ak-main-header-left .white-logo {
    display: none !important;
  }
  .ak-main-header-right {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .ak-site_header .container {
    max-width: 100%;
    background-color: var(--body-bg-color);
  }
  .ak-site_header.ak-style1 .ak-action_box > *:not(:last-child) {
    margin-right: 25px;
  }
  .ak-site_header.ak-style1 .ak-btn {
    padding: 8px;
  }
}
@media screen and (max-width: 575px) {
  .ak-site-branding {
    max-width: 150px;
  }
  .ak-site_branding img {
    max-height: 32px;
  }
  .ak-site_header.ak-style1 .ak-btn span {
    display: none;
  }
  .ak-site_header.ak-style1 .ak-btn svg {
    margin-right: 0;
    width: 20px;
    height: 20px;
  }
}
/*--------------------------------------------------------------
  9. Footer
----------------------------------------------------------------*/
.ak-footer.style-1 {
  position: relative;
  overflow: hidden;
}
.ak-footer.style-1 .footer-bgshape {
  position: absolute;
  right: 0;
  top: 0;
  background-position: top right;
}
.ak-footer.style-1 .ak-footer-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 155px;
}
@media (max-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container {
    gap: 50px;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-cta {
  display: flex;
  justify-content: space-between;
  gap: 50px;
  flex-wrap: wrap;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info {
  max-width: 850px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title {
  font-size: 120px;
  color: #ffffff;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
@media (min-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title {
    font-size: 8.2vw;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title-two {
  color: #ffffff;
  font-size: 100px;
  line-height: normal;
  font-weight: 400;
  font-style: italic;
  font-family: var(--secondary-font-family);
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title-two span {
  color: #ff4a23;
  position: relative;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title-two span::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 2px;
  bottom: 13px;
  right: 0;
  background-color: #ff4a23;
}
@media (min-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title-two {
    font-size: 8.3vw;
    line-height: 163px;
    width: 100%;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email {
  max-width: 371px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}
@media (max-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email {
    align-items: self-start;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-btn-content .footer-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  text-align: center;
  flex-wrap: wrap;
  width: 165px;
  height: 165px;
  padding: 62px 42px;
  border-radius: 50%;
  color: #ffffff;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 125%;
  text-transform: uppercase;
  background-color: var(--primary-color);
}
@media (max-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-btn-content .footer-btn {
    width: 135px;
    height: 135px;
    padding: 0px;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-btn-content .footer-btn i {
  line-height: 30%;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-email {
  display: flex;
  flex-direction: column;
  color: #ffffff;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-email .email-short-title {
  font-size: 18px;
  line-height: 165%;
}
.ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-email a {
  font-size: 45px;
  font-weight: 700;
  line-height: 125%;
  color: #ffffff;
}
.ak-footer.style-1 .ak-footer-container .footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 50px;
}
@media (min-width: 1199px) {
  .ak-footer.style-1 .ak-footer-container .footer-content .about-company {
    max-width: 522px;
  }
}
.ak-footer.style-1 .ak-footer-container .footer-content .about-company .footer-logo {
  max-width: 162px;
  max-height: 47px;
  margin-bottom: 25px;
}
.ak-footer.style-1 .ak-footer-container .footer-content .about-company .about-company-desp {
  font-size: 20px;
  line-height: 160%;
  color: #ffffff;
}
.ak-footer.style-1 .ak-footer-container .footer-content .about-company .about-company-desp span {
  color: #ff4a23;
  font-style: italic;
  text-decoration-line: underline;
}
.ak-footer.style-1 .ak-footer-container .footer-content .address-phn {
  color: #ffffff;
  max-width: 313px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.ak-footer.style-1 .ak-footer-container .footer-content .address-phn .phn {
  font-size: 35px;
  font-weight: 600;
  line-height: 130%;
  color: #ffffff;
}
.ak-footer.style-1 .ak-footer-container .footer-content .address-phn .phn i {
  line-height: normal;
}
.ak-footer.style-1 .ak-footer-container .footer-content .address-phn .address {
  font-size: 22px;
  font-style: normal;
  font-weight: 500;
  line-height: 150%;
}
.ak-footer.style-1 .ak-footer-container .footer-content .footer-list-content .footer-list-menu {
  color: #ffffff;
  font-size: 18px;
  line-height: 165%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  list-style: none;
  padding: 0;
  margin: 0;
  min-width: 150px;
}
.ak-footer.style-1 .ak-footer-container .footer-content .footer-list-content .footer-list-menu li {
  transform: translateX(0px);
  transition: transform 0.3s ease;
}
.ak-footer.style-1 .ak-footer-container .footer-content .footer-list-content .footer-list-menu li a {
  color: #ffffff;
  font-weight: 400;
}
.ak-footer.style-1 .ak-footer-container .footer-content .footer-list-content .footer-list-menu li:hover {
  transform: translateX(10px);
}
.ak-footer.style-1 .ak-footer-container .footer-content .footer-list-content .footer-list-menu li:hover a {
  opacity: 0.8;
}
.ak-footer.style-1 .copy-right-content {
  border-top: 1px solid rgba(53, 53, 53, 0.9);
  padding: 35px 0px;
}
.ak-footer.style-1 .copy-right-content .copy-right-social-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}
.ak-footer.style-1 .copy-right-content .copy-right-text {
  font-size: 18px;
  line-height: 165%;
  color: #ffffff;
  font-weight: 400;
}
.ak-footer.style-1 .copy-right-content .copy-right-text span {
  color: #ff4a23;
}

@media screen and (max-width: 576px) {
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title {
    font-size: 62px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-cta-info .footer-cta-title-two {
    font-size: 62px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-btn-content .footer-btn {
    width: 110px;
    height: 110px;
    font-size: 14px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-cta .footer-btn-email .footer-email a {
    font-size: 32px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-content .about-company .about-company-desp {
    font-size: 18px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-content .address-phn .phn {
    font-size: 32px;
  }
  .ak-footer.style-1 .ak-footer-container .footer-content .address-phn .address {
    font-size: 18px;
  }
  .ak-footer.style-1 .copy-right-content .ak-space-between {
    flex-wrap: wrap;
    gap: 20px;
  }
  .ak-footer.style-1 .copy-right-content .copy-right-text {
    font-size: 16px;
  }
}
/*--------------------------------------------------------------
  10. Pagination
----------------------------------------------------------------*/
.pagination-style {
  display: flex;
  gap: 30px;
  justify-content: center;
  color: #ffffff;
  font-size: 22px;
  cursor: pointer;
}
.pagination-style .current {
  color: #ff4a23;
}
.pagination-style .current.next,
.pagination-style .current.prev {
  color: #ffffff;
}

/*--------------------------------------------------------------
11. Section Heading Title
----------------------------------------------------------------*/
.ak-section-heading.ak-style-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 25px;
}
.ak-section-heading.ak-style-1.bg-black .ak-section-left .ak-section-title {
  color: #ffffff;
}
.ak-section-heading.ak-style-1.bg-black .ak-section-right .ak-section-desp {
  color: #c1c1c1;
}
.ak-section-heading.ak-style-1.bg-black .ak-section-right .ak-section-caption {
  color: #ffffff;
}
.ak-section-heading.ak-style-1 .ak-section-left {
  flex: 1 1 auto;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title {
  font-size: 55px;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 110%;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title.mini-section-title {
  font-size: 28px;
  font-weight: 600;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight {
  font-weight: 400;
  font-style: italic;
  color: #ff4a23;
  display: inline-block !important;
  font-family: var(--secondary-font-family);
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline::after {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background-color: #ff4a23;
  margin-top: 4px;
  opacity: 0;
  transition-delay: 1.5s;
  transition-duration: 0.5s;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline.active::after {
  opacity: 1;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline-white::after {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background-color: #ffffff;
  margin-bottom: 4px;
  opacity: 0;
  transition-delay: 1.5s;
  transition-duration: 0.5s;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline-white.active::after {
  opacity: 1;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline-black::after {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background-color: #101010;
  margin-bottom: 4px;
  opacity: 0;
  transition-delay: 1.5s;
  transition-duration: 0.5s;
}
.ak-section-heading.ak-style-1 .ak-section-left .ak-section-title .highlight.text-underline-black.active::after {
  opacity: 1;
}
.ak-section-heading.ak-style-1 .ak-section-right {
  flex: 1 1 auto;
  max-width: 600px;
  width: 100%;
}
.ak-section-heading.ak-style-1 .ak-section-right .ak-section-desp {
  font-size: 18px;
  font-style: italic;
  line-height: 165%;
  margin-bottom: 20px;
}
.ak-section-heading.ak-style-1 .ak-section-right .ak-section-caption {
  font-size: 16px;
  color: var(--heading-color);
  display: flex;
  gap: 20px;
  flex-shrink: 0;
  line-height: 165%;
}
.ak-section-heading.ak-style-1 .ak-section-right .ak-section-caption:first-child {
  padding-bottom: 15px;
}
.ak-section-heading.ak-style-1.type-2 .ak-section-right .ak-section-caption {
  justify-content: flex-end;
}

.breadcrumb-area.style-2 {
  position: relative;
  max-width: 1720px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.breadcrumb-area.style-2 .breadcrumb-stroke {
  text-transform: uppercase;
  position: absolute;
  top: 0;
  right: 10px;
  color: transparent;
  font-family: var(--heading-font-family);
  background-image: none;
  text-shadow: -1px -1px 0 var(--border-color), 0px -1px 0 var(--border-color), 1px -1px 0 var(--border-color), 1px 0px 0 var(--border-color), 1px 1px 0 var(--border-color), 0px 1px 0 var(--border-color), -1px 1px 0 var(--border-color), -1px 0 0 var(--border-color);
  color: var(--white-color);
  font-size: clamp(100px, 8vw, 150px);
  line-height: normal;
  font-weight: 700;
  z-index: 11;
}
.breadcrumb-area.style-2 .breadcrumb-stroke.text-normal {
  font-size: 130px;
  line-height: normal;
  color: var(--border-color);
  text-transform: uppercase;
}
.breadcrumb-area .breadcrumb-wapper {
  display: flex;
  align-items: center;
  gap: 50px;
  justify-content: flex-end;
}
.breadcrumb-area .breadcrumb-wapper.style-2 {
  justify-content: space-between;
}
.breadcrumb-area .breadcrumb-wapper.style-2 .breadcrumb-title-box {
  max-width: 100%;
  position: relative;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box {
  max-width: 831px;
  width: 100%;
  flex: 1 2 100%;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box .breadcrumb-title {
  font-size: clamp(42px, 5vw, 80px);
  font-style: normal;
  font-weight: 600;
  line-height: 110%;
  position: relative;
  z-index: 12;
  max-width: 1050px;
  width: 100%;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box .breadcrumb-caption {
  margin-top: 10px;
  width: 100%;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box .breadcrumb-caption a {
  font-size: 16px;
  transition: color 0.3s ease-in-out;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box .breadcrumb-caption a span {
  font-size: 16px;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box .breadcrumb-caption a:hover {
  color: #ff4a23;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box {
  max-width: 600px;
  width: 100%;
  position: relative;
  flex: 1 2 100%;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box video {
  width: 100%;
  height: 100%;
  border-radius: 1000px;
}
.breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box .breadcrumb-cricle {
  position: absolute;
  bottom: -50px;
  left: -50px;
}

.cricle-animated-text {
  position: relative;
  width: 180px;
  height: 180px;
  flex-shrink: 0;
  z-index: 13;
  border-radius: 50%;
  padding: 15px;
  background-color: var(--white-color);
}
.cricle-animated-text::before {
  position: absolute;
  display: inline-block;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background-color: var(--gray-bg);
  z-index: 1;
  border-radius: 50%;
}
.cricle-animated-text .cricle-ceneter-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75px;
  height: 75px;
  flex-shrink: 0;
  background-color: var(--white-color);
  border-radius: 50%;
}
.cricle-animated-text .rotating {
  animation: rotating 12s linear infinite;
  font-weight: 600;
  border-radius: 50%;
  background-color: var(--gray-bg);
  padding: 18px;
}
@media (max-width: 1199px) {
  .cricle-animated-text {
    width: 120px;
    height: 120px;
  }
  .cricle-animated-text::before {
    width: 20px;
    height: 20px;
  }
  .cricle-animated-text .cricle-ceneter-text {
    width: 45px;
    height: 45px;
  }
  .cricle-animated-text .rotating {
    padding: 10px;
  }
}
.cricle-animated-text .rounded-text {
  letter-spacing: 2px;
  font-weight: 500;
  z-index: -1;
  font-size: 18px;
}
.cricle-animated-text .rounded-text svg {
  fill: var(--black-color);
}

@keyframes rotating {
  from {
    transform: rotate(-360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
@media screen and (max-width: 1199px) {
  .ak-section-heading.ak-style-1 {
    flex-wrap: wrap;
  }
  .ak-section-heading.ak-style-1 .ak-section-right .ak-section-caption svg {
    max-width: 180px;
  }
  .ak-section-heading.ak-style-1.type-2 .ak-section-right .ak-section-caption {
    justify-content: flex-start;
  }
  .ak-section-heading.ak-style-1.type-2 .ak-section-right .ak-section-caption svg {
    max-width: 180px;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box {
    flex: 1 2 100%;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box {
    flex: 1 4 100%;
  }
}
@media screen and (max-width: 767px) {
  .ak-section-heading.ak-style-1 .ak-section-left .ak-section-title {
    font-size: 36px;
  }
  .breadcrumb-area .breadcrumb-wapper {
    flex-direction: column;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-caption svg {
    max-width: 150px;
  }
  .breadcrumb-area .breadcrumb-wapper.style-2 {
    align-items: flex-start;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-title-box {
    flex: 1 1 auto;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box {
    flex: 1 1 auto;
  }
  .breadcrumb-area .breadcrumb-wapper .breadcrumb-img-box video {
    border-radius: 0;
  }
}
/*--------------------------------------------------------------
  12. Button style
----------------------------------------------------------------*/
.btn-wrapper {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.btn-wrapper .button-container {
  width: 180px;
  height: 50px;
  position: absolute;
}
.btn-wrapper .button-container .custom-button {
  width: 180px;
  height: 50px;
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--border-color);
  outline: none;
  transition: 1s ease-in-out;
  border-radius: 50px;
  padding: 5px;
}
.btn-wrapper .button-container .custom-button:hover {
  transition: 1s ease-in-out;
}
.btn-wrapper .button-container .custom-button span {
  color: var(--black-color);
  font-size: 18px;
  font-weight: 500;
  text-transform: uppercase;
  line-height: 100%;
  margin-top: 3px;
}
.btn-wrapper .button-container .custom-button svg {
  position: absolute;
  left: 0;
  top: 0;
  fill: none;
  stroke: var(--black-color);
  stroke-dasharray: 150 480;
  stroke-dashoffset: 150;
  border-radius: 50px;
  transition: all 1s ease-in-out;
  overflow: hidden;
}
.btn-wrapper .button-container .custom-button svg:hover {
  stroke-dashoffset: -480;
}

.btn-8 {
  color: black;
  position: relative;
  overflow: hidden;
}
.btn-8:before, .btn-8:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: black;
  transition: transform 0.3s ease;
}
.btn-8:before {
  transform: translateY(-100%);
}
.btn-8:after {
  transform: translateY(100%);
}
.btn-8:hover {
  color: rgba(0, 0, 0, 0.75);
}
.btn-8:hover:before {
  transform: translateY(-50%);
}
.btn-8:hover:after {
  transform: translateY(50%);
}

.common-btn {
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
  border-radius: 50px;
  padding: 19px 25px;
  color: var(--black-color);
  border: 1px solid var(--border-color);
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
  position: relative;
  overflow: hidden;
}
.common-btn:hover {
  color: var(--white-color);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: scale(1.05);
}
.common-btn:hover::before {
  transform: translateX(0);
}
.common-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.dark .offcanvaopen-btn {
  background-color: #ffffff;
}
.dark .offcanvaopen-btn i {
  color: #101010;
}

.offcanvaopen-btn {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
}
.offcanvaopen-btn i {
  line-height: 100%;
  font-size: 21px;
  margin-top: 5px;
  transition: transform 0.4s ease;
  transform-origin: center;
}

.circle-btn {
  width: 165px;
  height: 165px;
  font-size: 18px;
  font-weight: 500;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  text-align: start;
  transition: background-color 0.3s, border-color 0.3s, border-width 0.3s;
  text-transform: uppercase;
}
.circle-btn:hover {
  color: var(--white-color);
  background-color: var(--black-color);
  transition: 0.5s background-color;
  border: 0 solid transparent;
}
.circle-btn.style-1 {
  background-color: var(--white-color);
}
.circle-btn.style-1:hover {
  color: var(--white-color);
  background-color: var(--black-color);
}
.circle-btn.style-2 {
  color: #ffffff;
  background-color: #ff4a23;
}
.circle-btn.style-2:hover {
  color: var(--white-color);
  background-color: var(--black-color);
}
.circle-btn.style-3 {
  color: #101010;
  background-color: #ffffff;
}
.circle-btn.style-3:hover {
  color: #ffffff;
  background-color: #101010;
}
.circle-btn.style-4 {
  color: #ffffff;
  background-color: #ff4a23;
}
.circle-btn.style-4:hover {
  background-color: #ffffff;
  color: #101010;
}

.more-btn {
  position: relative;
  display: inline-flex;
  color: var(--black-color);
  font-size: 16px;
  font-weight: 500;
  min-width: 100px;
  text-transform: uppercase;
  margin-bottom: 6px;
  flex-shrink: 0;
  cursor: pointer;
  align-items: center;
}
.more-btn.style2 {
  border-radius: 50px;
  border: 2px solid var(--border-color);
  display: inline-flex;
  padding: 19px 25px;
  font-style: normal;
  font-weight: 500;
  overflow: hidden;
}
.more-btn.style2 .text-1 {
  position: relative;
  transition: all 0.3s;
  transform: scale3d(1, 1, 1) rotate3d(1, 0, 0, 0deg);
}
.more-btn.style2:hover .text-1 {
  transform: scale3d(1.05, 1.05, 1) translateX(2%) rotate3d(1, 0, 1, 10deg);
}
.more-btn.style2::after {
  display: none;
}
.more-btn.style3 {
  border: none;
}
.more-btn.style3::after {
  display: none;
}
.more-btn .svg-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 111;
  overflow: hidden;
  transform: rotate3d(0, 0, 1, -45deg);
}
.more-btn .svg-icon > :first-child {
  transition: all 0.3s;
  transform: translateX(-150%);
}
.more-btn .svg-icon > :last-child {
  transition: all 0.3s;
  transform: translateX(-50%);
}
.more-btn .svg-icon i {
  font-size: 20px;
  color: #ff4a23;
}
.more-btn .svg-icon.style-three {
  transform: rotate3d(0, 0, 1, 0deg);
}
.more-btn::after {
  content: "";
  position: absolute;
  display: inline;
  width: 0%;
  height: 1px;
  left: 0;
  bottom: 5px;
  opacity: 1;
  transition: all 0.3s;
  background-color: var(--black-color);
}
.more-btn .morebtn-text {
  font-size: 16px;
  margin-top: 3px;
}
.more-btn .primary-icon-anim i {
  font-size: 17px;
}
.more-btn:hover {
  color: var(--black-color);
}
.more-btn:hover .svg-icon.style-two {
  transform: rotate3d(0, 0, 1, 0deg);
}
.more-btn:hover .svg-icon > :first-child {
  transform: translateX(50%);
}
.more-btn:hover .svg-icon > :last-child {
  transform: translateX(170%) rotateX(50deg);
}
.more-btn:hover::after {
  width: 70%;
}
.more-btn:hover .primary-icon-anim i:nth-of-type(1) {
  transform: translate(40px, -60px);
  opacity: 0;
  color: #ff4a23;
}
.more-btn:hover .primary-icon-anim i:nth-of-type(2) {
  transform: translate(0, 0);
  opacity: 1;
  color: #ff4a23;
}
.more-btn.arrow-left-style::after {
  right: 0;
  left: auto;
}
.more-btn.arrow-left-style .svg-icon {
  transform: rotate3d(0, 0, 1, 0deg);
}
.more-btn.arrow-left-style .svg-icon > :first-child {
  transition: all 0.3s;
  transform: translateX(210%);
}
.more-btn.arrow-left-style .svg-icon > :last-child {
  transition: all 0.3s;
  transform: translateX(-50%);
}
.more-btn.arrow-left-style:hover .svg-icon > :first-child {
  transform: translateX(50%);
}
.more-btn.arrow-left-style:hover .svg-icon > :last-child {
  transform: translateX(-210%) rotateX(50deg);
}

.primary-icon-anim {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.primary-icon-anim i {
  position: absolute;
  font-size: larger;
  line-height: 30%;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  color: #ff4a23;
}
.primary-icon-anim i:nth-of-type(1) {
  transform: translate(0, 0);
  opacity: 1;
}
.primary-icon-anim i:nth-of-type(2) {
  transform: translate(-40px, 60px);
  opacity: 0;
}

.arrow-circle-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
  cursor: pointer;
}
.arrow-circle-btn svg {
  transition: margin 0.3s ease-in-out;
}
.arrow-circle-btn svg path {
  fill: var(--black-color);
}
.arrow-circle-btn.prev:hover {
  border: 1px solid var(--primary-color);
}
.arrow-circle-btn.prev:hover svg {
  margin-right: 10px;
}
.arrow-circle-btn.prev:hover svg path {
  fill: var(--primary-color);
}
.arrow-circle-btn.next:hover {
  border: 1px solid var(--primary-color);
}
.arrow-circle-btn.next:hover svg {
  margin-left: 10px;
}
.arrow-circle-btn.next:hover svg path {
  fill: var(--primary-color);
}

@media screen and (min-width: 1399px) {
  .btn-flip-text {
    perspective: 1000px;
    text-decoration: none;
  }
  .btn-flip-text:hover span {
    transform: rotateX(90deg) translateY(-12px);
  }
  .btn-flip-text span {
    position: relative;
    display: inline-block;
    padding: 0;
    transition: transform 0.3s ease, color 0.3s ease;
    transform-origin: 50% 0;
    transform-style: preserve-3d;
  }
  .btn-flip-text span::before {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 100%;
    content: attr(data-text);
    transition: color 0.3s ease;
    transform: rotateX(-90deg);
    transform-origin: 50% 0;
    text-align: center;
  }
}
/*End Dark mode btn */
@media screen and (max-width: 1199px) {
  .circle-btn {
    width: 150px;
    height: 150px;
  }
}
@media screen and (max-width: 767px) {
  .circle-btn {
    width: 130px;
    height: 130px;
    font-size: 16px;
  }
}
/*--------------------------------------------------------------
  13. Hero
----------------------------------------------------------------*/
.dark .design-company-hero-area {
  background-color: transparent;
}

.digital-agencye-hero.style-1 {
  display: flex;
  gap: 40px;
  width: 100%;
  margin: 0 auto;
  font-size: 18px;
}
@media (min-width: 1720px) {
  .digital-agencye-hero.style-1 {
    width: calc(100vw - 100px);
  }
}
.digital-agencye-hero.style-1 .hero-left-column {
  max-width: 765px;
  flex: 1 3 auto;
}
.digital-agencye-hero.style-1 .hero-left-column .partners-section {
  display: flex;
  flex-direction: column;
  gap: 80px;
  position: relative;
}
.digital-agencye-hero.style-1 .hero-left-column .partners-section .partners-title {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
}
.digital-agencye-hero.style-1 .hero-left-column .partners-section .partners-title::after {
  position: absolute;
  content: "";
  height: 290px;
  width: 1px;
  z-index: 11;
  right: -41px;
  bottom: 31px;
  background: linear-gradient(180deg, #ff4a23 0%, transparent 100%);
}
.digital-agencye-hero.style-1 .hero-left-column .partners-section .partners-title:before {
  position: absolute;
  content: "";
  height: 1px;
  width: 60%;
  right: -40px;
  top: 17px;
  background: linear-gradient(90deg, transparent 20%, #ff4a23 100%);
}
.digital-agencye-hero.style-1 .hero-left-column .cta-box {
  display: flex;
  gap: 60px;
  align-items: center;
  max-width: 771px;
  margin-bottom: 100px;
}
.digital-agencye-hero.style-1 .hero-left-column .title-box {
  margin-bottom: 45px;
}
.digital-agencye-hero.style-1 .hero-left-column .title-box .title {
  margin-top: 70px;
}
.digital-agencye-hero.style-1 .hero-left-column .title-box .title .digital {
  font-size: 125px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: uppercase;
}
.digital-agencye-hero.style-1 .hero-left-column .title-box .title .agency {
  font-size: 175px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: uppercase;
  color: var(--primary-color);
}
.digital-agencye-hero.style-1 .hero-right-column {
  max-width: 997px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 55px;
  justify-content: space-between;
  flex: 1 2 auto;
}
.digital-agencye-hero.style-1 .hero-right-column .description {
  max-width: 789px;
  padding-left: 63px;
}
.digital-agencye-hero.style-1 .hero-right-column .image-box {
  border: 1px solid var(--border-color);
  padding: 63px 0 63px 63px;
  width: 100%;
  height: 100%;
  position: relative;
}
.digital-agencye-hero.style-1 .hero-right-column .image-box .da-shape-star {
  position: absolute;
  left: -89px;
  top: -89px;
}
.digital-agencye-hero.style-1 .hero-right-column .image-box .hero-right-image {
  width: 100%;
  height: 100%;
  background-color: var(--white-color);
  position: absolute;
  bottom: 0;
}
.digital-agencye-hero.style-1 .hero-right-column .image-box video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  max-width: 100%;
  display: block;
}
.digital-agencye-hero.style-1 .hero-right-column .image-box img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card {
  position: relative;
  min-height: 100vh;
  width: 100%;
  height: 100%;
}
@media (min-height: 800px) {
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card {
    min-height: calc(100vh - 120px);
  }
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .hero-bg-img {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 11;
  -o-object-fit: cover;
     object-fit: cover;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content {
  position: absolute;
  width: 100%;
  bottom: 112px;
  z-index: 12;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info {
  text-transform: uppercase;
  max-width: 1203px;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info .marketing-agency-caption {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  color: #101010;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info .main-title {
  font-size: 100px;
  line-height: 130%;
  font-style: normal;
  color: #101010;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info .main-title .highlight {
  font-size: 100px;
  font-family: var(--secondary-font-family);
  color: #ff4a23;
  font-weight: 400;
  font-style: italic;
  text-decoration-line: underline;
  text-decoration-style: solid;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  text-transform: uppercase;
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info .sub-title {
  font-size: 70px;
  line-height: 130%;
  font-style: normal;
  color: #101010;
}
.marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card .marketing-agency-content .marketing-agency-info .sub-title .highlight {
  font-weight: 400;
  font-size: 100px;
  font-family: var(--secondary-font-family);
  color: #ff4a23;
  font-style: italic;
  text-decoration-line: underline;
  text-decoration-style: solid;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  text-transform: uppercase;
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}
.marketing-agency-section .marketing-agency-hero.style-1 .social-links {
  position: absolute;
  width: 100px;
  height: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--white-color);
  z-index: 12;
}
.marketing-agency-section .marketing-agency-hero.style-1 .social-links ul {
  height: 100%;
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.marketing-agency-section .marketing-agency-hero.style-1 .social-links ul li {
  margin-bottom: 20px;
}
.marketing-agency-section .marketing-agency-hero.style-1 .social-links ul li a.social-link {
  display: inline-block;
  font-size: 16px;
  text-transform: uppercase;
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  text-decoration: none;
}
.marketing-agency-section .marketing-agency-hero.style-1 .social-links ul li a.social-link:hover {
  color: #ff4a23;
}
.marketing-agency-section .marketing-agency-cta {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -80px;
  z-index: 12;
  gap: 30px;
}
.marketing-agency-section .marketing-agency-cta .cta-content {
  margin-top: 80px;
  max-width: 460px;
  width: 100%;
}
.marketing-agency-section .marketing-agency-cta .cta-circle {
  background-color: var(--white-color);
  border-radius: 50%;
  padding: 22px;
}
.marketing-agency-section .marketing-agency-cta .contact-info {
  margin-top: 80px;
  max-width: 486px;
  width: 100%;
  display: flex;
  gap: 15px;
  justify-content: space-between;
  flex-wrap: wrap;
}
.marketing-agency-section .marketing-agency-cta .contact-info .email-info a,
.marketing-agency-section .marketing-agency-cta .contact-info .phone-info a {
  font-size: 22px;
  color: var(--black-color);
  font-weight: 500;
  transition: color 0.3s;
}
.marketing-agency-section .marketing-agency-cta .contact-info .email-info a:hover,
.marketing-agency-section .marketing-agency-cta .contact-info .phone-info a:hover {
  color: #ff4a23;
}
@media (min-width: 1199px) {
  .marketing-agency-section .container-extent {
    max-width: 100%;
    margin: 0 5% 0 10%;
  }
}
.marketing-agency-section .container-extent {
  height: 100%;
  padding: 0 15px;
}

.design-company-hero-area {
  padding-top: 210px;
  position: relative;
  background-color: #f1f1f1;
}
.design-company-hero-area .dc-hero-wrapper.style-1 {
  position: relative;
  z-index: 2;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box {
  display: flex;
  flex-direction: column;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-1 {
  font-size: 259.117px;
  font-style: italic;
  font-weight: 700;
  line-height: 110%;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-2 {
  font-size: 234.659px;
  font-style: normal;
  font-weight: 700;
  line-height: 110%;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content {
  display: flex;
  gap: 48px;
  align-items: center;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content .dc-hero-title-img {
  width: 378px;
  height: 198px;
  flex-shrink: 0;
  border-radius: 500px;
  overflow: hidden;
  position: relative;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content .dc-hero-title-img video {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-bottom-content {
  display: flex;
  gap: 48px;
  align-items: center;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-bottom-content .dc-hero-desp {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 165%;
  text-align: justify;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box {
  display: flex;
  justify-content: space-between;
  max-width: 1037px;
  width: 100%;
  align-items: center;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box .dc-hero-info-left {
  max-width: 165px;
  width: 100%;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box .dc-hero-info-right {
  max-width: 486px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box .dc-hero-info-right p {
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
}
.design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box .dc-hero-info-right a {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
}

.minimal-studio-hero-area {
  display: flex;
  position: relative;
}
.minimal-studio-hero-area .ms-hero-title-content {
  max-width: 1520px;
  width: 100%;
  margin-bottom: 35px;
}
.minimal-studio-hero-area .ms-hero-title-content .ms-hero-title {
  font-weight: 400;
  font-style: normal;
  font-size: clamp(55px, 8vw, 123px);
}
.minimal-studio-hero-area .ms-hero-title-content .ms-hero-title .highlight {
  font-style: italic;
  color: #ff4a23;
}
.minimal-studio-hero-area .ms-hero-title-content .ms-hero-title .highlight-black {
  font-size: clamp(65px, 8vw, 149px);
  font-style: italic;
  font-weight: 700;
}
.minimal-studio-hero-area .ms-animated-badge {
  position: absolute;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  z-index: 13;
  border-radius: 50%;
  background-color: var(--white-color);
  padding: 20px;
  right: 0;
  bottom: -90px;
}
.minimal-studio-hero-area .ms-animated-badge .ms-ceneter-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75px;
  height: 75px;
  flex-shrink: 0;
  background-color: var(--white-color);
  border-radius: 50%;
}
.minimal-studio-hero-area .ms-animated-badge .ms-ceneter-text::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  height: 50%;
  border-radius: 50%;
  background-color: #f1f1f1;
}
.minimal-studio-hero-area .ms-animated-badge .rotating {
  animation: rotating 12s linear infinite;
  font-weight: 600;
  border-radius: 50%;
  background-color: #641200;
  padding: 18px;
}
@media (max-width: 1400px) {
  .minimal-studio-hero-area .ms-animated-badge {
    width: 120px;
    height: 120px;
    bottom: -75px;
    padding: 12px;
  }
  .minimal-studio-hero-area .ms-animated-badge .ms-ceneter-text {
    width: 25px;
    height: 25px;
  }
  .minimal-studio-hero-area .ms-animated-badge .rotating {
    padding: 10px;
  }
}
.minimal-studio-hero-area .ms-animated-badge .rounded-text {
  letter-spacing: 2px;
  font-weight: 500;
  z-index: -1;
  font-size: 18px;
}
.minimal-studio-hero-area .ms-animated-badge .rounded-text svg {
  fill: #ffffff;
}

.seo-agency-hero-area {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  height: 100%;
  width: 100%;
}
.seo-agency-hero-area .seo-agency-hero-area-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: brightness(0.5);
}
.seo-agency-hero-area .seo-agency-wrapper {
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (min-width: 1400px) {
  .seo-agency-hero-area .seo-agency-wrapper {
    max-width: 1341px;
  }
}
.seo-agency-hero-area .seo-agency-wrapper .sa-title-box {
  display: flex;
  margin-bottom: 45px;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-title-box .sa-title {
  color: #ffffff;
  font-size: clamp(75px, 8vw, 138px);
  font-weight: 700;
  line-height: 108%;
  font-weight: 500;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-title-box .sa-title .highlight {
  font-family: var(--secondary-font-family);
  font-style: italic;
  font-weight: 400;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-contact-info {
  display: flex;
  max-width: 486px;
  width: 100%;
  gap: 20px;
  justify-content: space-between;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-contact-info .sa-email,
.seo-agency-hero-area .seo-agency-wrapper .sa-contact-info .sa-phone {
  color: #c1c1c1;
  font-size: 14px;
  font-weight: 400;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-contact-info .sa-email a,
.seo-agency-hero-area .seo-agency-wrapper .sa-contact-info .sa-phone a {
  color: #ffffff;
  font-size: 22px;
  font-weight: 500;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge {
  position: relative;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
  z-index: 13;
  border-radius: 50%;
  background-color: #101010;
  padding: 20px;
  right: 0;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .ms-ceneter-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75px;
  height: 75px;
  flex-shrink: 0;
  background-color: #101010;
  border-radius: 50%;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .ms-ceneter-text::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40%;
  height: 40%;
  border-radius: 50%;
  background-color: #f1f1f1;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .rotating {
  animation: rotating 12s linear infinite;
  font-weight: 600;
  border-radius: 50%;
  background-color: #641200;
  padding: 18px;
}
@media (max-width: 991px) {
  .seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge {
    width: 120px;
    height: 120px;
    bottom: -90px;
    padding: 12px;
  }
  .seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .ms-ceneter-text {
    width: 25px;
    height: 25px;
  }
  .seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .rotating {
    padding: 10px;
  }
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .rounded-text {
  letter-spacing: 2px;
  font-weight: 500;
  z-index: -1;
  font-size: 18px;
}
.seo-agency-hero-area .seo-agency-wrapper .sa-animated-rounded-badge .rounded-text svg {
  fill: #ffffff;
}
.seo-agency-hero-area .sa-left-text-content {
  position: absolute;
  width: 100px;
  top: 50%;
  transform: translate(0%, -50%);
  left: 30px;
  z-index: 12;
}
.seo-agency-hero-area .sa-left-text-content .sa-left-text {
  display: inline-block;
  writing-mode: vertical-lr;
  transform: rotate(180deg);
  color: #c1c1c1;
}
.seo-agency-hero-area .sa-social-links {
  position: absolute;
  width: 100px;
  top: 50%;
  transform: translate(0%, -50%);
  right: -20px;
  z-index: 12;
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.seo-agency-hero-area .sa-social-links a {
  display: inline-block;
  writing-mode: vertical-lr;
  transform: rotate(180deg);
  text-decoration: none;
  color: #ffffff;
  transition: color 0.5s ease;
}
.seo-agency-hero-area .sa-social-links a:hover {
  color: #ff4a23;
}
.seo-agency-hero-area .sa-social-links a .sa-social-item {
  cursor: pointer;
  font-size: 16px;
  text-transform: uppercase;
  color: #ffffff;
}
.seo-agency-hero-area .sa-social-links a .sa-social-item:hover {
  color: #ff4a23;
}

.creactive-portflio-slider {
  position: relative;
  min-height: 100vh;
  height: 100%;
}
.creactive-portflio-slider .cp-hero-content {
  min-height: calc(100vh - 100px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.creactive-portflio-slider .cp-hero-content img {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.creactive-portflio-slider .cp-hero-content .cp-hero-title-box {
  position: absolute;
  max-width: 991px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 100px;
}
.creactive-portflio-slider .cp-hero-content .cp-hero-title-box .cp-hero-title {
  font-size: clamp(48px, 6vw, 100px);
  font-style: normal;
  font-weight: 700;
  line-height: 110%;
  text-transform: uppercase;
  color: #101010;
}
.creactive-portflio-slider .cp-social-links {
  position: absolute;
  width: 100px;
  top: 50%;
  padding-top: 140px;
  transform: translate(0%, -50%);
  height: 100%;
  z-index: 122;
  list-style: none;
  left: -16px;
  display: flex;
  flex-direction: column;
  gap: 50px;
  background-color: var(--white-color);
  align-items: center;
}
.creactive-portflio-slider .cp-social-links .sa-social-item {
  display: inline-block;
  writing-mode: vertical-lr;
  transform: rotate(180deg);
  text-decoration: none;
}
.creactive-portflio-slider .cp-social-links .sa-social-item a {
  cursor: pointer;
  font-size: 16px;
  text-transform: uppercase;
  color: var(--black-color);
  transition: color 0.3s ease-in-out;
}
.creactive-portflio-slider .cp-social-links .sa-social-item a:hover {
  color: #ff4a23;
}
.creactive-portflio-slider .cta-box {
  background-color: var(--white-color);
  max-width: 945px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 100;
  bottom: 0;
}
.creactive-portflio-slider .cta-box .cta-content {
  position: relative;
  margin-left: 85px;
}
.creactive-portflio-slider .cta-box .cta-content .dot-box .left-dot,
.creactive-portflio-slider .cta-box .cta-content .dot-box .right-dot,
.creactive-portflio-slider .cta-box .cta-content .dot-box .top-bot,
.creactive-portflio-slider .cta-box .cta-content .dot-box .bottom-dot {
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: #ff4a23;
}
.creactive-portflio-slider .cta-box .cta-content .dot-box .left-dot {
  left: 0;
}
.creactive-portflio-slider .cta-box .cta-content .dot-box .right-dot {
  right: 0;
}
.creactive-portflio-slider .cta-box .cta-content .dot-box .top-bot {
  left: 0;
  bottom: 0;
}
.creactive-portflio-slider .cta-box .cta-content .dot-box .bottom-dot {
  right: 0;
  bottom: 0;
}
.creactive-portflio-slider .cta-box .cta-content .cta-info {
  display: flex;
  padding: 35px 150px 35px 42px;
  align-items: center;
}
.creactive-portflio-slider .cp-swiper-pagination {
  position: relative;
  z-index: 11;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 35px;
  font-size: 28px;
  color: #c1c1c1;
  gap: 10px;
  font-weight: 600;
}
.creactive-portflio-slider .cp-swiper-pagination .swiper-pagination-current {
  color: var(--black-color);
}

.circle-360 {
  animation: animationglob 5s cubic-bezier(1, 0.99, 0.03, 0.01) infinite;
}

@keyframes animationglob {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotating {
  from {
    transform: rotate(-360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
@media screen and (max-width: 1399px) {
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-1 {
    font-size: 200px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-2 {
    font-size: 150px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content {
    gap: 36px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content .dc-hero-title-img {
    width: 280px;
    height: 150px;
  }
  .creactive-portflio-slider .cp-swiper-pagination {
    display: none;
  }
}
@media screen and (max-width: 1199px) {
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card {
    min-height: 60vh;
  }
  .marketing-agency-section .marketing-agency-cta {
    margin-top: -60px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box {
    margin-bottom: 45px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title {
    margin-top: 50px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title .digital {
    font-size: clamp(92px, 22vw, 120px);
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title .agency {
    font-size: clamp(100px, 22vw, 120px);
  }
  .digital-agencye-hero.style-1 .hero-left-column .cta-box {
    margin-bottom: 50px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .cta-box .circle-btn {
    width: 100px;
    height: 100px;
    font-size: 14px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .partners-section {
    gap: 50px;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content {
    height: 100%;
    bottom: -40px !important;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info .main-title {
    font-size: 55px !important;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info .main-title .highlight {
    font-size: 65px !important;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info .sub-title {
    font-size: 55px !important;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info .sub-title .highlight {
    font-size: 65px !important;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-content .marketing-agency-info .marketing-agency-caption svg {
    max-width: 180px;
  }
  .marketing-agency-section .marketing-agency-hero.style-1 .social-links {
    display: none;
  }
  .creactive-portflio-slider {
    min-height: 75vh;
  }
  .creactive-portflio-slider .cp-hero-content {
    min-height: 65vh;
  }
  .creactive-portflio-slider .cp-swiper-pagination {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .digital-agencye-hero.style-1 {
    flex-direction: column;
    gap: 50px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box {
    margin-bottom: 45px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title {
    margin-top: 45px;
    line-height: 1px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title .digital {
    font-size: clamp(62px, 22vw, 120px);
  }
  .digital-agencye-hero.style-1 .hero-left-column .title-box .title .agency {
    font-size: clamp(72px, 22vw, 120px);
  }
  .digital-agencye-hero.style-1 .hero-left-column .cta-box {
    margin-bottom: 50px;
    flex-wrap: wrap;
    flex-direction: column-reverse;
    align-items: flex-start;
  }
  .digital-agencye-hero.style-1 .hero-left-column .cta-box .circle-btn {
    width: 130px;
    height: 130px;
    font-size: 16px;
  }
  .digital-agencye-hero.style-1 .hero-left-column .partners-section {
    gap: 50px;
  }
  .digital-agencye-hero.style-1 .hero-right-column .description {
    padding: 15px !important;
  }
  .digital-agencye-hero.style-1 .hero-right-column .image-box {
    padding: 20px;
    height: 400px;
  }
  .design-company-hero-area {
    padding-top: 150px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-1 {
    font-size: 140px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-2 {
    font-size: 120px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content {
    gap: 24px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content .dc-hero-title-img {
    width: 200px;
    height: 120px;
  }
  .creactive-portflio-slider .cp-hero-content .cp-hero-title-box {
    max-width: 100%;
    margin-left: 0;
    text-align: center;
  }
  .creactive-portflio-slider .cp-social-links {
    display: none;
  }
  .creactive-portflio-slider .cta-box {
    max-width: 80%;
    transform: translate(-50%, 0%);
    left: 50%;
  }
  .creactive-portflio-slider .cta-box .cta-content {
    margin-left: 0;
  }
  .creactive-portflio-slider .cta-box .cta-content .cta-info {
    padding: 50px;
  }
  .seo-agency-hero-area .sa-left-text-content {
    display: none;
  }
  .seo-agency-hero-area .sa-social-links {
    display: none;
  }
  .seo-agency-hero-area .sa-animated-rounded-badge {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  .marketing-agency-section .marketing-agency-hero.style-1 .marketing-agency-slider-card {
    min-height: 100vh;
  }
  .marketing-agency-section .marketing-agency-cta {
    margin-top: 60px;
    flex-wrap: wrap;
    gap: 0px;
  }
  .marketing-agency-section .marketing-agency-cta .cta-content {
    margin-top: 0;
  }
  .marketing-agency-section .marketing-agency-cta .contact-info {
    margin-top: 0;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box {
    gap: 15px;
    margin-bottom: 30px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-1 {
    font-size: 100px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-title.text-2 {
    font-size: 80px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content {
    gap: 16px;
    flex-direction: column-reverse;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-top-content .dc-hero-title-img {
    width: 85vw;
    height: 150px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-title-box .dc-hero-bottom-content {
    flex-direction: column-reverse;
    gap: 16px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box {
    align-items: flex-start;
    gap: 16px;
  }
  .design-company-hero-area .dc-hero-wrapper.style-1 .dc-hero-info-box .dc-hero-info-right {
    flex-direction: column;
    align-items: flex-start;
  }
  .creactive-portflio-slider {
    min-height: 80vh;
    max-height: 85vh;
  }
  .creactive-portflio-slider .cp-hero-content {
    min-height: calc(100vh - 100px);
  }
  .creactive-portflio-slider .cp-hero-content img {
    -o-object-fit: cover;
       object-fit: cover;
  }
  .creactive-portflio-slider .cta-box {
    max-width: 95%;
  }
  .creactive-portflio-slider .cta-box .cta-content .cta-info {
    padding: 15px;
  }
  .creactive-portflio-slider .cta-box .cta-content .cta-info .circle-btn {
    display: none;
  }
  .seo-agency-hero-area .seo-agency-wrapper .sa-title-box {
    margin-top: 50px;
    margin-bottom: 15px;
  }
  .seo-agency-hero-area .seo-agency-wrapper .sa-contact-info {
    flex-direction: column;
  }
}
/*--------------------------------------------------------------
14. Service
----------------------------------------------------------------*/
.service-bg {
  background-color: var(--gray-bg);
}

.service-card {
  position: relative;
  padding-top: 75px;
  padding-bottom: 50px;
  border-bottom: 1px solid rgba(53, 53, 53, 0.2);
}
.service-card .service-card-item.style-1 {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  flex-wrap: wrap;
}
.service-card .service-card-item.style-1 .service-left-info .service-title {
  margin-bottom: 38px;
  font-size: 35px;
  font-weight: 600;
  line-height: 130%;
}
.service-card .service-card-item.style-1 .service-left-info .service-lists {
  margin: 0;
  text-decoration: none;
  list-style: none;
  padding: 0;
}
.service-card .service-card-item.style-1 .service-left-info .service-lists .service-list {
  font-size: 18px;
}
.service-card .service-card-item.style-1 .service-left-right {
  max-width: 386px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 30px;
}
.service-card .service-card-item.style-1 .service-left-right .service-desp {
  font-size: 16px;
}
.service-card .service-stroke-number {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  font-style: italic;
}
.service-card .service-hover-img {
  position: absolute;
  left: 35%;
  width: auto;
  max-height: 280px;
  height: 100%;
  bottom: 0;
  z-index: 11;
}
.service-card::after {
  content: "";
  position: absolute;
  max-width: 249px;
  max-height: 249px;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(180deg, #bababa 0%, rgba(186, 186, 186, 0) 56.13%);
  bottom: -50%;
  left: 50%;
  transform: translate(-50%, 0%);
  opacity: 0.2;
}

.services-branding {
  position: relative;
}
.services-branding .sb-card,
.services-branding div {
  position: relative;
  display: inline-block;
}
.services-branding .sb-card h2,
.services-branding div h2 {
  position: relative;
  z-index: 2;
  cursor: pointer;
  font-size: 55px;
  font-style: normal;
  font-weight: 600;
  line-height: 170%;
  transition: color 0.2s ease;
}
.services-branding .sb-card h2:hover,
.services-branding div h2:hover {
  color: #ff4a23;
}
.services-branding .sb-card img,
.services-branding div img {
  position: absolute;
  top: 50%;
  left: 60%;
  transform: translate(-50%, -50%) scale(0.5);
  opacity: 0;
  z-index: 1;
  transition: opacity 0.2s ease, transform 0.2s ease;
  pointer-events: none;
}

.dm-service-items {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(8, 1fr);
  grid-gap: 1.5rem;
}
.dm-service-items.style2 {
  display: flex;
  width: 100%;
}
.dm-service-items.style2 .service-item {
  width: 100%;
  max-height: 500px;
}
.dm-service-items.style2 .service-item img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.dm-service-items img {
  width: 100%;
  height: 120%;
  -o-object-fit: cover;
     object-fit: cover;
}
.dm-service-items .width-1 {
  grid-column: span 2;
  grid-row: span 4;
  overflow: hidden;
}
.dm-service-items .width-2 {
  grid-column: span 4;
  grid-row: span 4;
  overflow: hidden;
}
.dm-service-items .service-item {
  position: relative;
  overflow: hidden;
}
.dm-service-items .service-item .service-hover-info {
  max-width: 405px;
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--white-color);
  padding: 29px 0 29px 42px;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  transition: opacity 0.3s;
}
.dm-service-items .service-item .service-hover-info .left-content .mini-title {
  font-size: 18px;
  border-radius: 50px;
  display: inline-flex;
  padding: 2px 15px 0px 15px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border: 1px solid var(--secondary-color);
  margin-bottom: 10px;
  color: var(--secondary-color);
}
.dm-service-items .service-item .service-hover-info .left-content .title {
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 130%;
  color: var(--black-color);
}
.dm-service-items .service-item .service-hover-info .service-icon-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid #ff4a23;
  background-color: transparent;
  transition: background-color 0.3s ease-in-out;
}
.dm-service-items .service-item .service-hover-info .service-icon-btn i {
  transition: transform 0.3s ease-in-out;
  line-height: normal;
  color: #ff4a23;
  transform: rotate3d(0, 0, 1, 0deg);
}
.dm-service-items .service-item .service-hover-info .service-icon-btn:hover {
  background-color: #ff4a23;
  border: 1px solid transparent;
}
.dm-service-items .service-item .service-hover-info .service-icon-btn:hover i {
  color: var(--white-color);
  transform: rotate3d(0, 0, 1, 360deg);
}
.dm-service-items .service-item:hover .service-hover-info {
  opacity: 1;
}
@media (max-width: 575px) {
  .dm-service-items .service-item .service-hover-info {
    display: none;
  }
}

.seo-service-wapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 100px;
}
.seo-service-wapper .seo-service-img-content {
  max-width: 475px;
  width: 100%;
}
.seo-service-wapper ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.seo-service-wapper .seo-service-lists {
  max-width: 845px;
  width: 100%;
}
.seo-service-wapper .seo-service-lists .service-list-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  transition: border-bottom 0.3s;
  padding: 32px 0;
  border-bottom: 1px solid var(--border-color);
}
.seo-service-wapper .seo-service-lists .service-list-item .service-list-title {
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 130%;
  transition: font-style 0.5s;
  color: var(--black-color);
}
.seo-service-wapper .seo-service-lists .service-list-item:hover {
  border-bottom: 1px solid #ff4a23;
}
.seo-service-wapper .seo-service-lists .service-list-item:hover .service-list-title {
  color: #ff4a23;
}

.methodology-wrapper {
  position: relative;
}
.methodology-wrapper .background-text {
  width: 100%;
  position: absolute;
  font-size: 10rem;
  font-weight: bold;
  color: rgba(255, 69, 58, 0.05);
  top: 50%;
  left: 60%;
  transform: translate(60%, 50%);
  transition: transform 0.1s ease-out;
}

.methodology-card {
  background: conic-gradient(from 180deg at 50% 50%, #f1f1f1 219deg, #fff 304deg);
  padding: 70px 48px;
}
.methodology-card .methodology-number {
  color: #ff4a23;
  margin-bottom: 45px;
  font-style: italic;
  font-family: var(--secondary-font-family);
}
.methodology-card .methodology-info .methodology-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 130%;
  color: #101010;
  margin-bottom: 18px;
}

.services-main-img {
  width: 85vw;
}

.services-short-info {
  margin: 50px 0;
}
.services-short-info-content {
  max-width: 1081px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 30px;
  flex-wrap: wrap;
}
.services-short-info-item {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}
.services-short-info-label {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
  color: var(--black-color);
  position: relative;
  padding-left: 25px;
}
.services-short-info-label::after {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  left: 0;
  top: 25%;
  background-color: #ff4a23;
}
.services-short-info-text {
  font-size: 18px;
  font-weight: 400;
  line-height: 165%;
  padding-left: 25px;
}

.services-details-title-text {
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 125%;
  margin-bottom: 20px;
}
.services-details-title-description {
  font-size: 18px;
  line-height: 165%;
}

.dark .methodology-card {
  background: conic-gradient(from 180deg at 50% 50%, #410b00 0deg, #641200 90deg);
}
.dark .methodology-card .methodology-number {
  color: #ffffff;
}
.dark .methodology-card .methodology-info .methodology-title {
  color: #ffffff;
}

@media screen and (max-width: 1199px) {
  .service-card .service-stroke-number {
    display: none;
  }
  .service-card::after {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .services-main-img {
    width: 100%;
  }
  .service-card {
    padding-top: 50px;
    padding-bottom: 30px;
  }
  .service-card .service-card-item.style-1 .service-left-info .service-title {
    font-size: 28px;
    margin-bottom: 30px;
  }
  .service-card .service-card-item.style-1 .service-left-right {
    gap: 15px;
  }
  .seo-service-wapper {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }
  .seo-service-wapper .seo-service-img-content {
    max-width: 100%;
    max-height: 400px;
  }
  .seo-service-wapper .seo-service-img-content img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
  .seo-service-wapper .seo-service-lists .service-list-item {
    padding: 25px 0;
    flex-direction: column;
    gap: 20px;
  }
  .seo-service-wapper .seo-service-lists .service-list-item .service-list-title {
    font-size: 24px;
  }
}
@media screen and (max-width: 767px) {
  .seo-service-wapper .seo-service-lists .service-list-item .more-btn:hover::after {
    width: 20%;
  }
  .services-branding .sb-card h2,
  .services-branding div h2 {
    font-size: 28px;
  }
  .services-main-img {
    width: 100%;
  }
  .services-main-img img {
    height: 350px;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
/*--------------------------------------------------------------
15. Moving Text
----------------------------------------------------------------*/
.slideing-text-content {
  position: relative;
  height: 270px;
  left: -89px;
}
.slideing-text-content .slideing-text {
  position: absolute;
  font-weight: 700;
  font-size: 100px;
  letter-spacing: 0;
  line-height: 120px;
  white-space: nowrap;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}
.slideing-text-content .text-color-one {
  top: 0;
  left: 0;
  text-shadow: -1px -1px 0 #ff4a23, 0px -1px 0 #ff4a23, 1px -1px 0 #ff4a23, 1px 0px 0 #ff4a23, 1px 1px 0 #ff4a23, 0px 1px 0 #ff4a23, -1px 1px 0 #ff4a23, -1px 0 0 #ff4a23;
  color: #ff4a23;
}
.slideing-text-content .text-color-two {
  top: 149px;
  left: -150px;
  background-image: none;
  text-shadow: -1px -1px 0 #01010f, 0px -1px 0 #01010f, 1px -1px 0 #01010f, 1px 0px 0 #01010f, 1px 1px 0 #01010f, 0px 1px 0 #01010f, -1px 1px 0 #01010f, -1px 0 0 #01010f;
  color: #fff;
}
.slideing-text-content.style2 .text-color-three,
.slideing-text-content.style2 .text-color-two {
  background-image: none;
  text-shadow: -1px -1px 0 #01010f, 0px -1px 0 #01010f, 1px -1px 0 #01010f, 1px 0px 0 #01010f, 1px 1px 0 #01010f, 0px 1px 0 #01010f, -1px 1px 0 #01010f, -1px 0 0 #01010f;
  color: #fff;
}

@keyframes scrollText {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-100%);
  }
}
.background-gradient {
  padding: 20px;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  background: rgb(255, 74, 35);
  background: linear-gradient(90deg, rgba(255, 74, 35, 0.0760679272) 0%, rgba(255, 74, 35, 0.3365721289) 50%, rgba(255, 74, 35, 0.0816701681) 100%);
}

.text-container {
  display: inline-flex;
  animation: scrollText 10s linear infinite;
  position: relative;
  width: 100%;
  white-space: nowrap;
}

.text-gradient {
  transform: translateX(0px);
  font-weight: bold;
  background: linear-gradient(90deg, #ff4500, #000000, #ff4500);
  -webkit-background-clip: text;
  background-clip: text;
  font-size: 55px;
  line-height: 120%;
  -webkit-text-fill-color: transparent;
  margin-right: 1rem;
}

.dark .text-gradient {
  transform: translateX(0px);
  font-weight: bold;
  background: linear-gradient(90deg, #ff4500, #ffffff, #ff4500);
  -webkit-background-clip: text;
  background-clip: text;
  font-size: 55px;
  line-height: 120%;
  -webkit-text-fill-color: transparent;
  margin-right: 1rem;
}

.text-moving-container.style2 {
  position: relative;
}
.text-moving-container.style2 .text-moving-bg {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--white-color) 0%, rgba(255, 74, 35, 0) 50%, var(--white-color) 100%);
  z-index: 1;
}

.text-moving-info {
  display: inline-flex;
  animation: scrollText 10s linear infinite;
  position: relative;
  width: 100%;
  white-space: nowrap;
}
.text-moving-info .text-moving.style2 {
  transform: translateX(0px);
  font-weight: bold;
  background: #ff4a23;
  -webkit-background-clip: text;
  background-clip: text;
  font-size: 100px;
  line-height: 120%;
  -webkit-text-fill-color: transparent;
  margin-right: 1rem;
}

@media screen and (max-width: 991px) {
  .slideing-text-content {
    height: 200px;
  }
  .slideing-text-content .slideing-text {
    font-size: 75px;
  }
  .slideing-text-content .text-color-one {
    top: 0;
    left: -337px;
  }
  .slideing-text-content .text-color-two {
    top: 100px;
    left: -244px;
  }
}
@media screen and (max-width: 767px) {
  .slideing-text-content {
    height: 155px;
  }
  .slideing-text-content .slideing-text {
    font-size: 52px;
  }
  .slideing-text-content .text-color-one {
    top: 0;
    left: -337px;
  }
  .slideing-text-content .text-color-two {
    top: 68px;
    left: -244px;
  }
  .text-moving-container.style2 {
    position: relative;
  }
  .text-moving-container.style2 .text-moving-bg {
    display: none;
  }
}
/*--------------------------------------------------------------
16. About Content
----------------------------------------------------------------*/
.dark .about-content .star-content .star-1 {
  opacity: 0.3;
}
.dark .about-content .star-content .star-2 {
  opacity: 0.3;
}
.dark .about-content .about-info .swirl {
  opacity: 0.1;
}

.about-content {
  position: relative;
  height: 408px;
  display: flex;
  align-items: center;
  overflow: hidden;
}
@media (min-width: 1400px) {
  .about-content {
    max-width: 1420px;
    margin-left: 48px;
  }
}
.about-content .star-content {
  width: 335px;
  display: flex;
  flex-direction: column;
}
.about-content .star-content .star-1 {
  max-width: 150px;
  align-self: self-start;
}
.about-content .star-content .star-2 {
  max-width: 247px;
  align-self: self-end;
}
.about-content .about-info {
  max-width: 1117px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}
.about-content .about-info .about-title {
  font-size: 42px;
  font-style: normal;
  font-weight: 600;
  position: relative;
  z-index: 13;
  margin-bottom: 78px;
  line-height: 130%;
  color: var(--black-color);
}
.about-content .about-info .about-title .highlight {
  font-weight: 400;
  line-height: 130%;
  font-style: italic;
  font-family: var(--secondary-font-family);
  color: #ff4a23 !important;
}
.about-content .about-info .more-btn {
  z-index: 13;
}
.about-content .about-info .more-btn .svg-icon i {
  font-size: 22px;
  color: #ff4a23;
}
.about-content .about-info .swirl {
  position: absolute;
  top: 46px;
  max-width: 895px;
  z-index: 12;
}

.about-content-style2 {
  width: calc(82vw - 100px);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}
.about-content-style2 .about-title {
  font-size: 55px;
  font-style: italic;
  font-weight: 400;
  line-height: normal;
  position: relative;
  font-family: var(--secondary-font-family);
  color: transparent;
  -webkit-text-stroke: 1px #c1c1c1;
  left: 20px;
}
.about-content-style2 .about-title::after {
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 10px;
  height: 10px;
  flex-shrink: 0;
  background-color: #ff4a23;
}
.about-content-style2 .about-info {
  max-width: 1086px;
  width: 100%;
}
.about-content-style2 .about-info .about-desp {
  font-size: 32px;
  font-weight: 400;
  line-height: 150%;
  margin-bottom: 50px;
}
.about-content-style2 .about-info .about-desp .highlight {
  color: #ff4a23;
  font-style: italic;
  font-family: var(--secondary-font-family);
}

.about-circle-area {
  display: flex;
  gap: 50px;
  justify-content: space-between;
}
.about-circle-area .about-circle-content {
  display: flex;
  flex-direction: column;
  width: 581px;
  justify-content: flex-start;
  margin-top: 45px;
  gap: 30px;
}
.about-circle-area .about-circle-content .list-text {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}
.about-circle-area .about-circle-content .list-text li {
  width: 50%;
  box-sizing: border-box;
}
.about-circle-area .about-circle-content .leads-list {
  list-style: none;
  padding: 0;
  margin: 0;
  counter-reset: service-counter;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.about-circle-area .about-circle-content .leads-list li a {
  font-size: 35px;
  font-style: normal;
  font-weight: 600;
  line-height: 130%;
  text-transform: uppercase;
  color: var(--black-color);
  transition: color 0.3s;
  cursor: pointer;
  border-width: 0;
  position: relative;
}
.about-circle-area .about-circle-content .leads-list li a::after {
  width: 0;
  height: 1px;
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  transition: width 0.5s;
  display: inline-block;
  background-color: #ff4a23;
}
.about-circle-area .about-circle-content .leads-list li a:hover {
  color: #ff4a23;
}
.about-circle-area .about-circle-content .leads-list li a:hover::after {
  width: 100%;
}
.about-circle-area .about-circle-img {
  width: 450px;
  height: 450px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex-shrink: 0;
}
.about-circle-area .about-circle-img .about-circle-img-info {
  padding: 0 47px;
}
.about-circle-area .about-circle-img .about-circle-img-info .title {
  color: #ffffff;
  margin-bottom: 10px;
}
.about-circle-area .about-circle-img .about-circle-img-info .desp {
  color: #c1c1c1;
}

.seo-agency-about-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.seo-agency-about-wrapper .seo-agency-about-content {
  max-width: 610px;
  width: 100%;
}
.seo-agency-about-wrapper .seo-agency-about-content .about-title {
  font-size: 55px;
  font-style: italic;
  font-weight: 400;
  line-height: normal;
  position: relative;
  font-family: var(--secondary-font-family);
  color: transparent;
  -webkit-text-stroke: 1px #c1c1c1;
  left: 20px;
}
.seo-agency-about-wrapper .seo-agency-about-content .about-title::after {
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 10px;
  height: 10px;
  flex-shrink: 0;
  background-color: #ff4a23;
}
.seo-agency-about-wrapper .seo-agency-about-content .about-info {
  max-width: 610px;
  width: 100%;
}
.seo-agency-about-wrapper .seo-agency-about-content .about-info .about-desp {
  font-size: 32px;
  font-weight: 400;
  line-height: 150%;
  margin-bottom: 30px;
}
.seo-agency-about-wrapper .seo-agency-about-content .about-info .about-desp .highlight {
  color: #ff4a23;
  font-style: italic;
  font-family: var(--secondary-font-family);
}
.seo-agency-about-wrapper .number-circle-content {
  display: flex;
  width: 650px;
  height: 713px;
  flex-wrap: wrap;
  position: relative;
}
.seo-agency-about-wrapper .number-circle {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex-shrink: 0;
}
.seo-agency-about-wrapper .number-circle .img-clip-text {
  line-height: 100%;
  font-size: 100px;
}
.seo-agency-about-wrapper .number-circle .achievement-text {
  font-size: 18px;
}
.seo-agency-about-wrapper .number-circle-center-right {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(0%, -50%);
}
.seo-agency-about-wrapper .number-circle-bottom {
  position: absolute;
  left: 0;
  bottom: 0;
}

@media screen and (max-width: 1199px) {
  .about-content {
    height: 100%;
  }
  .about-content .about-info .swirl {
    max-width: 50%;
  }
  .about-content .about-info .about-title {
    font-size: 35px;
    margin-bottom: 25px;
  }
  .about-content .star-content .star-1 {
    max-width: 100px;
  }
  .about-content .star-content .star-2 {
    max-width: 150px;
  }
  .about-content-style2 {
    width: 100%;
  }
}
@media screen and (max-width: 991px) {
  .about-content {
    flex-direction: column-reverse;
  }
  .about-content .star-content {
    position: absolute;
    width: 100%;
  }
  .about-circle-area {
    flex-direction: column;
  }
  .about-circle-area .about-circle-img {
    width: 100%;
    height: 300px;
    border-radius: 0;
  }
  .seo-agency-about-wrapper {
    flex-direction: column;
  }
  .seo-agency-about-wrapper .number-circle-content {
    height: auto;
    width: 100%;
    justify-content: space-between;
    gap: 30px;
  }
  .seo-agency-about-wrapper .number-circle-content {
    flex-wrap: wrap;
  }
  .seo-agency-about-wrapper .number-circle {
    width: 200px;
    height: 200px;
  }
  .seo-agency-about-wrapper .number-circle .img-clip-text {
    font-size: 55px;
  }
  .seo-agency-about-wrapper .number-circle .achievement-text {
    font-size: 16px;
  }
  .seo-agency-about-wrapper .number-circle-center-right {
    position: relative;
    top: 0;
    transform: translate(0%, 0%);
  }
  .seo-agency-about-wrapper .number-circle-bottom {
    position: relative;
  }
}
@media screen and (max-width: 767px) {
  .about-content {
    flex-direction: column-reverse;
  }
  .about-content .about-info .about-title {
    font-size: 28px;
  }
  .about-content .star-content .star-1 {
    max-width: 50px;
  }
  .about-content .star-content .star-2 {
    max-width: 100px;
  }
  .about-content-style2 .about-info .about-desp {
    font-size: 22px;
  }
  .about-circle-area .about-circle-content .leads-list li {
    font-size: 24px;
  }
  .seo-agency-about-wrapper {
    flex-direction: column;
  }
  .seo-agency-about-wrapper .seo-agency-about-content .about-title {
    font-size: 55px;
  }
  .seo-agency-about-wrapper .seo-agency-about-content .about-info .about-desp {
    font-size: 22px;
  }
  .seo-agency-about-wrapper .number-circle-content {
    flex-direction: column;
    align-items: center;
  }
}
/*--------------------------------------------------------------
  17. Counter Funfact
----------------------------------------------------------------*/
.funfact-content {
  display: flex;
}
.funfact-content > * {
  width: 24.4%;
}
.funfact-content.type-2 > * {
  width: 25%;
}

.funfact-card.style-1 {
  width: 350px;
  height: 350px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 350px;
  border: 1px solid var(--border-color);
}
.funfact-card.style-1.type-2 {
  width: 100%;
  height: auto;
  border: 0;
  border-radius: 0px;
}
.funfact-card.style-1.type-2 .funfact-number {
  -webkit-text-stroke: 0px var(--body-color);
  text-shadow: none !important;
}
.funfact-card.style-1.type-3 {
  text-align: center;
}
.funfact-card.style-1.type-3 .funfact-text {
  font-size: 18px;
  line-height: 165%;
}
.funfact-card.style-1.type-3 .funfact-number {
  color: var(--black-color);
  font-size: 55px;
  margin-top: 10px;
}
.funfact-card.style-1 .funfact-number {
  font-style: normal;
  line-height: normal;
  color: transparent;
  font-size: 100px;
  font-weight: 700;
  font-family: var(--heading-font-family);
  background-image: none;
  text-shadow: -1px -1px 0 var(--black-color), 0px -1px 0 var(--black-color), 1px -1px 0 var(--black-color), 1px 0px 0 var(--black-color), 1px 1px 0 var(--black-color), 0px 1px 0 var(--black-color), -1px 1px 0 var(--black-color), -1px 0 0 var(--black-color);
  color: var(--white-color);
  word-spacing: -32px;
}
.funfact-card.style-1 .funfact-text {
  font-size: 18px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .funfact-card.style-1 {
    width: 300px;
    height: 300px;
  }
  .funfact-card.style-1 .funfact-number {
    font-size: 75px;
  }
}
@media screen and (max-width: 1199px) {
  .funfact-card.style-1 {
    width: 250px;
    height: 250px;
  }
  .funfact-card.style-1 .funfact-number {
    font-size: 55px;
  }
}
@media screen and (max-width: 991px) {
  .funfact-content {
    justify-content: center;
    flex-wrap: wrap;
  }
  .funfact-content.funfact-gap {
    gap: 50px;
  }
  .funfact-content > * {
    width: 250px !important;
  }
}
/*--------------------------------------------------------------
18. Feature Content
----------------------------------------------------------------*/
.dark .core-feature-card {
  background: conic-gradient(from 180deg at 50% 50%, #410b00 0deg, #641200 90deg);
}
.dark .core-feature-card.type-2 {
  background: conic-gradient(from 180deg at 50% 50%, #410b00 0deg, #641200 90deg);
}
.dark .core-feature-card.type-3 {
  background: conic-gradient(from 180deg at 50% 50%, #410b00 0deg, #641200 90deg);
}

.core-features-area .core-features {
  display: flex;
  justify-content: space-between;
  gap: 50px;
}
.core-features-area .core-features > * {
  flex: 1 1 33.33%;
}
.core-features-area .core-features.style2 {
  gap: 30px;
}

.core-feature-card {
  background: conic-gradient(from 180deg at 50% 50%, #f1f1f1 234deg, #fff 270deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60px 23px 10px 23px;
  max-width: 385px;
  position: relative;
  z-index: 111;
}
.core-feature-card.type-2 {
  background: conic-gradient(from 180deg at 50% 50%, #f1f1f1 166deg, #fff 227deg);
}
.core-feature-card.type-3 {
  background: conic-gradient(from 180deg at 50% 50%, #f1f1f1 12deg, #fff 270deg);
}
.core-feature-card.style-2 .icon i {
  color: #ffffff;
}
.core-feature-card.style-2 .core-feature-title {
  color: #ffffff;
}
.core-feature-card.style-2 .core-feature-desp {
  color: #ffffff;
}
.core-feature-card.style-2 .core-feature-number {
  color: #ffffff;
}
.core-feature-card.type-1 {
  background: conic-gradient(from 180deg at 50% 50%, #410b00 0deg, #641200 90deg);
}
.core-feature-card.color-2 {
  background: #101010;
}
.core-feature-card.color-3 {
  background: #ff4a23;
}
.core-feature-card .core-feature-title {
  font-size: 22px;
  margin-bottom: 5px;
  line-height: 150%;
}
.core-feature-card .core-feature-desp {
  font-size: 16px;
  margin-bottom: 35px;
}
.core-feature-card .icon {
  margin-bottom: 26px;
}
.core-feature-card .icon i {
  font-size: 60px;
}
.core-feature-card .icon i path {
  fill: var(--black-color);
}
.core-feature-card .core-feature-number {
  font-size: 25px;
  font-style: italic;
  font-family: var(--secondary-font-family);
  margin-bottom: 10px;
}

.core-features-swiper-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.core-features-swiper-pagination .swiper-pagination-bullet {
  width: 13px;
  height: 13px;
  margin: 0 5px;
  border-radius: 50%;
  border: 2px solid #ff4a23;
  transition: transform 0.3s ease, border-color 0.3s ease;
}
.core-features-swiper-pagination .swiper-pagination-bullet-active {
  background: #ff4a23;
  border: 2px solid #ff4a23;
}

.feature-area {
  position: relative;
  overflow: hidden;
}
.feature-area .feature-area-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: 1;
  transition: background-color 1s linear;
}
.feature-area .feature-wapper {
  position: relative;
  z-index: 2;
}
.feature-area .feature-wapper .feature-item {
  position: relative;
  height: 750px;
  background-color: transparent;
  overflow: hidden;
}
.feature-area .feature-wapper .feature-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2;
  pointer-events: none;
  transition: background-color 0.5s linear;
}
.feature-area .feature-wapper .feature-item.active::before {
  background-color: rgba(0, 0, 0, 0);
}
.feature-area .feature-wapper .feature-item .feature-item-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
  opacity: 0;
}
.feature-area .feature-wapper .feature-item .feature-item-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 95px 45px 65px 50px;
  z-index: 12;
}
.feature-area .feature-wapper .feature-item .feature-item-number .feature-number {
  font-style: normal;
  line-height: normal;
  font-size: 100px;
  font-weight: 700;
  font-family: var(--heading-font-family);
  background-image: none;
  text-shadow: -1px -1px 0 #ffffff, 0px -1px 0 #ffffff, 1px -1px 0 #ffffff, 1px 0px 0 #ffffff, 1px 1px 0 #ffffff, 0px 1px 0 #ffffff, -1px 1px 0 #ffffff, -1px 0 0 #ffffff;
  color: #101010;
  word-spacing: -32px;
  opacity: 0.7;
}
.feature-area .feature-wapper .feature-item:hover .feature-info {
  margin-bottom: 20px;
}
.feature-area .feature-wapper .feature-item:hover .feature-info .feature-info-desp {
  opacity: 1;
}
.feature-area .feature-wapper .feature-item .feature-info {
  transition: all 0.5s ease;
  margin-bottom: -50px;
}
.feature-area .feature-wapper .feature-item .feature-info .feature-info-title {
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 130%;
  color: #ffffff;
}
.feature-area .feature-wapper .feature-item .feature-info .feature-info-desp {
  font-size: 18px;
  margin-top: 22px;
  opacity: 0;
  transition: all 0.5s ease;
  color: #c1c1c1;
}

@media screen and (max-width: 1199px) {
  .core-features {
    gap: 50px;
    flex-wrap: wrap;
  }
  .feature-item {
    height: 450px;
  }
  .core-feature-card {
    max-width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .feature-area .feature-area-img {
    opacity: 0;
  }
  .feature-area .feature-wapper .feature-item {
    height: 550px;
  }
  .feature-area .feature-wapper .feature-item .feature-item-bg {
    opacity: 1;
    -o-object-fit: cover;
       object-fit: cover;
    background-repeat: no-repeat;
  }
  .feature-area .feature-wapper .feature-item .feature-item-content {
    padding: 60px 30px 30px 30px;
  }
  .core-features-area .core-features {
    flex-direction: column;
    gap: 50px;
  }
  .core-features-area .core-features > * {
    flex: 1 1 100%;
  }
  .core-features {
    justify-content: center;
    gap: 50px;
    flex-direction: column;
  }
  .core-features > * {
    flex: 1 1 100%;
  }
  .core-feature-card {
    max-width: 100%;
  }
}
/*--------------------------------------------------------------
19. Testmonial
----------------------------------------------------------------*/
.testmonial-content.style-1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  border: 1px solid var(--border-color);
}
.testmonial-content.style-1 .testmonial-desp {
  font-size: 18px;
  font-style: italic;
  line-height: 165%;
  text-align: center;
  padding: 0px 55px;
  color: var(--black-color);
}
.testmonial-content.style-1 .testmonial-info {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.testmonial-content.style-1 .testmonial-info .testmonial-img {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 50%;
  text-align: center;
  margin-bottom: 30px;
}
.testmonial-content.style-1 .testmonial-info .testmonial-title {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
  text-align: center;
}
.testmonial-content.style-1 .testmonial-info .testmonial-shot-desp {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 165%;
  text-align: center;
}

.testmonial-swiper-controller {
  display: flex;
}
.testmonial-swiper-controller .testmonial-button-prev {
  justify-content: flex-end;
}
.testmonial-swiper-controller .testmonial-button-prev > * {
  max-width: 650px;
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}
.testmonial-swiper-controller .testmonial-button-prev > * span {
  position: relative;
  z-index: 111;
  transition: color 0.3s ease;
}
.testmonial-swiper-controller .testmonial-button-prev > *:before, .testmonial-swiper-controller .testmonial-button-prev > *:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: var(--black-color);
  transition: transform 0.3s ease;
  z-index: 11;
}
.testmonial-swiper-controller .testmonial-button-prev > *:before {
  transform: translateY(-100%);
}
.testmonial-swiper-controller .testmonial-button-prev > *:after {
  transform: translateY(100%);
}
.testmonial-swiper-controller .testmonial-button-prev > *:hover {
  color: var(--white-color);
}
.testmonial-swiper-controller .testmonial-button-prev > *:hover:before {
  transform: translateY(-50%);
}
.testmonial-swiper-controller .testmonial-button-prev > *:hover:after {
  transform: translateY(50%);
}
.testmonial-swiper-controller .testmonial-button-next {
  justify-content: flex-start;
}
.testmonial-swiper-controller .testmonial-button-next > * {
  max-width: 650px;
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}
.testmonial-swiper-controller .testmonial-button-next > * span {
  position: relative;
  z-index: 111;
  transition: color 0.3s ease;
}
.testmonial-swiper-controller .testmonial-button-next > *:before, .testmonial-swiper-controller .testmonial-button-next > *:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: var(--black-color);
  transition: transform 0.3s ease;
  z-index: 11;
}
.testmonial-swiper-controller .testmonial-button-next > *:before {
  transform: translateY(-100%);
}
.testmonial-swiper-controller .testmonial-button-next > *:after {
  transform: translateY(100%);
}
.testmonial-swiper-controller .testmonial-button-next > *:hover {
  color: var(--white-color);
}
.testmonial-swiper-controller .testmonial-button-next > *:hover:before {
  transform: translateY(-50%);
}
.testmonial-swiper-controller .testmonial-button-next > *:hover:after {
  transform: translateY(50%);
}
.testmonial-swiper-controller .testmonial-button-prev,
.testmonial-swiper-controller .testmonial-button-next {
  width: 100%;
  height: 86px;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  color: var(--black-color);
  text-transform: uppercase;
  font-size: 18px;
  line-height: normal;
  transition: background-color 0.3s;
}

.testmonial-area {
  position: relative;
}
.testmonial-area .testmonial-bg-shape {
  position: absolute;
  left: 0;
  top: -81%;
  transform: translate(-2%, 50%);
  width: auto;
  height: auto;
  z-index: 11;
}
.testmonial-area .testmonial-area-img {
  position: absolute;
  width: 100%;
  height: 120%;
  z-index: 10;
  top: -50px;
}

.testmonial-wrapper {
  padding: 100px 0;
  position: relative;
  z-index: 13;
}
.testmonial-wrapper.style2 {
  padding: 200px 0;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 {
  flex-direction: row-reverse;
  gap: 100px;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-person-content {
  flex-direction: column;
  flex-shrink: 0;
  align-items: flex-start;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-desp-content {
  margin-bottom: 65px;
  padding: 0;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-desp-content .desp {
  text-align: start;
  font-size: 32px;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content {
  position: relative;
  margin: 50px;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content .desp {
  font-size: 36px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%;
  text-align: center;
  color: #ffffff;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content .desp.color-2 {
  color: var(--black-color);
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content .desp .highlight {
  font-style: italic;
  color: #ff4a23;
  font-family: var(--secondary-font-family);
  text-decoration-line: underline;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content {
  display: flex;
  gap: 20px;
  align-items: center;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content .person-img {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 80px;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content .person-info h6 {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
  color: #ffffff;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content .person-info p {
  font-size: 18px;
  line-height: 165%;
  color: #c1c1c1;
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content .person-info.color-2 h6 {
  color: var(--black-color);
}
.testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-person-content .person-info.color-2 p {
  color: var(--secondary-color);
}
.testmonial-wrapper .testmonial-slider-controller-2 {
  text-align: center;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 13;
  margin-top: 75px;
}
.testmonial-wrapper .testmonial-slider-controller-2.type-2 {
  margin: 0;
}
.testmonial-wrapper .testmonial-slider-controller-2 > * {
  max-width: 262px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn p {
  font-size: 18px;
  text-transform: uppercase;
  color: #c1c1c1;
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn .more-btn.style3 .svg-icon {
  margin-top: 5px;
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn.ts-prev-2 .btn-text, .testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn.ts-next-2 .btn-text {
  color: var(--secondary-color);
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn.ts-prev-2 .btn-text.color-white, .testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn.ts-next-2 .btn-text.color-white {
  color: #ffffff !important;
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn.color-2 p {
  color: var(--secondary-color);
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn .btn-text {
  color: var(--white-color);
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn:hover .btn-text {
  color: var(--primary-color);
}
.testmonial-wrapper .testmonial-slider-controller-2 .testmonial-slider-btn:hover .btn-text.color-white {
  color: var(--black-color);
}

@media screen and (max-width: 991px) {
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content {
    padding: 0;
    margin-bottom: 30px;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content .desp {
    text-align: center;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 {
    flex-direction: column-reverse;
    gap: 50px;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-person-content {
    margin-bottom: 0;
    align-items: center;
    text-align: center;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-desp-content .desp {
    text-align: center;
    line-height: 124%;
  }
}
@media screen and (max-width: 767px) {
  .testmonial-wrapper {
    padding: 80px 0;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 {
    flex-direction: column-reverse;
    gap: 30px;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-person-content {
    margin-bottom: 0;
    align-items: center;
    text-align: center;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-desp-content {
    margin-bottom: 50px;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2.type-2 .testmonial-desp-content .desp {
    font-size: 24px;
    text-align: center;
    line-height: 124%;
  }
  .testmonial-wrapper .testmonial-slider-2 .testmonial-content.style-2 .testmonial-desp-content .desp {
    font-size: 24px;
    line-height: 130%;
  }
}
/*--------------------------------------------------------------
20. Team
----------------------------------------------------------------*/
.team-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
}
.team-card .team-img-top {
  position: relative;
}
.team-card .team-img-top img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.team-card .team-img-top .team-social-icon {
  position: absolute;
  bottom: 25px;
  left: 40%;
  transform: translate(-50%, -50%);
  display: inline-flex;
  opacity: 0;
  transition: all 0.5s;
}
.team-card .team-img-top .team-social-icon .icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  background-color: rgba(255, 255, 255, 0.7);
  transition: background-color 0.3s;
}
.team-card .team-img-top .team-social-icon .icon i {
  font-size: 20px;
  color: #101010;
  display: contents;
}
.team-card .team-img-top .team-social-icon .icon:hover {
  background-color: #ff4a23;
}
.team-card .team-img-top .team-social-icon .icon:hover i {
  color: #ffffff;
}
.team-card .team-body {
  flex: 1 1 auto;
  margin: 20px 0px 0px 30px;
}
.team-card .team-body .team-title,
.team-card .team-body .team-text {
  transition: all 0.3s;
  line-height: 165%;
}
.team-card .team-body .team-text {
  font-size: 16px;
}
.team-card:hover .team-img-top .team-social-icon {
  opacity: 1;
  left: 50%;
}
.team-card:hover .team-body .team-title {
  color: #ff4a23;
}

.dark .team-swiper-controller .team-logs-navigation .team-logs-button-prev,
.dark .team-swiper-controller .team-logs-navigation .team-logs-button-next {
  border: 1px solid #ffffff;
}
.dark .team-swiper-controller .team-logs-navigation .team-logs-button-prev svg path,
.dark .team-swiper-controller .team-logs-navigation .team-logs-button-next svg path {
  fill: #ffffff;
}

.team-swiper-controller {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.team-swiper-controller .team-logs-scrollbar {
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  position: absolute;
  top: 50%;
}
.team-swiper-controller .team-logs-navigation {
  display: flex;
  gap: 25px;
  flex-shrink: 0;
  z-index: 111;
  background-color: var(--white-color);
  transition: all 0.5s;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev,
.team-swiper-controller .team-logs-navigation .team-logs-button-next {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  transition: border 0.2s ease-in-out;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev svg,
.team-swiper-controller .team-logs-navigation .team-logs-button-next svg {
  transition: all 0.2s ease-in-out;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev svg path,
.team-swiper-controller .team-logs-navigation .team-logs-button-next svg path {
  transition: fill 0.2s ease-in-out;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev i,
.team-swiper-controller .team-logs-navigation .team-logs-button-next i {
  line-height: normal;
  font-size: 25px;
  transition: all 0.3s ease-in-out;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev:hover {
  border: 1px solid var(--primary-color);
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev:hover svg {
  margin-right: 10px;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-prev:hover svg path {
  fill: var(--primary-color);
}
.team-swiper-controller .team-logs-navigation .team-logs-button-next:hover {
  border: 1px solid var(--primary-color);
}
.team-swiper-controller .team-logs-navigation .team-logs-button-next:hover svg {
  margin-left: 10px;
}
.team-swiper-controller .team-logs-navigation .team-logs-button-next:hover svg path {
  fill: var(--primary-color);
}

@media screen and (max-width: 767px) {
  .team-card {
    max-width: 380px;
  }
}
/*--------------------------------------------------------------
21. DarkMode Theme Btn
----------------------------------------------------------------*/
.mode-toggle {
  position: relative;
  font-size: 18px;
  cursor: pointer;
  z-index: 10000;
}
.mode-toggle .setting-mode {
  position: fixed;
  right: 0px;
  top: 40%;
  -webkit-transition: all 0.2s;
  transform: translateY(-50%);
  transition: all 0.2s;
  cursor: pointer;
  mix-blend-mode: exclusion;
}
.mode-toggle .setting-mode button {
  border: none;
  line-height: normal;
  background-color: #2b2b2f;
  padding: 7px;
  border-radius: 5px;
}
.mode-toggle .setting-mode button i {
  display: inline-block;
  line-height: normal;
  color: #ffffff;
  margin-top: 3px;
  font-size: 13px;
}
.mode-toggle .setting-mode #clecel {
  display: none;
}
.mode-toggle .mode-btn {
  position: fixed;
  right: -50px;
  top: 40%;
  transform: translateY(-50%);
  transition: all 0.3s;
  mix-blend-mode: exclusion;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 50px;
}
.mode-toggle .mode-btn button {
  border: none;
  background-color: #2b2b2f;
  color: rgba(143, 143, 143, 0.9333333333);
  line-height: normal;
}
.mode-toggle .mode-btn button i {
  line-height: normal;
}
.mode-toggle .mode-btn button.mode-light {
  padding: 8px 8px 4px 8px;
}
.mode-toggle .mode-btn button.mode-dark {
  padding: 4px 8px 8px 8px;
}
.mode-toggle .mode-btn .active {
  color: #fff;
}

.offcanvas-logo-content .dark-logo {
  display: block;
}
.offcanvas-logo-content .white-logo {
  display: none;
}

.dark .circle-btn.style-4 {
  background-color: #ffffff;
  color: #101010;
}
.dark .circle-btn.style-4:hover {
  background-color: #101010;
  color: #ffffff;
}
.dark .offcanvas-logo-content .dark-logo {
  display: none;
}
.dark .offcanvas-logo-content .white-logo {
  display: block;
}
.dark .ak-site_header .ak-main_header .ak-main_header_in .ak-main-header-left .dark-logo {
  display: none;
}
.dark .ak-site_header .ak-main_header .ak-main_header_in .ak-main-header-left .white-logo {
  display: block;
}
.dark .ak-site_header.style-2.type2.ak-gescout_sticky .ak-main-header-left .ak-site_branding.dark-logo {
  display: none;
}
.dark .ak-site_header.style-2.type2.ak-gescout_sticky .ak-main-header-left .ak-site_branding.white-logo {
  display: block;
}

.ak-site_header .ak-main_header .ak-main_header_in .ak-main-header-left .dark-logo {
  display: block;
}
.ak-site_header .ak-main_header .ak-main_header_in .ak-main-header-left .white-logo {
  display: none;
}

@media screen and (min-width: 1200px) {
  .ak-site_header.style-2.type2 .ak-main-header-center .ak-nav .ak-nav_list.typle2 ul a {
    color: var(--white-color);
  }
  .ak-site_header.style-2.type2 .ak-main-header-center .ak-nav .ak-nav_list.typle2 ul a:hover {
    color: var(--primary-color);
  }
}
.ak-site_header.style-2.type2 .ak-main-header-right .btn-wrapper .button-container span {
  color: #ffffff;
}
.ak-site_header.style-2.type2 .ak-main-header-left .ak-site_branding.dark-logo {
  display: none;
}
.ak-site_header.style-2.type2 .ak-main-header-left .ak-site_branding.white-logo {
  display: block;
}
.ak-site_header.style-2.type2.ak-gescout_sticky .ak-main-header-left .ak-site_branding.dark-logo {
  display: block;
}
.ak-site_header.style-2.type2.ak-gescout_sticky .ak-main-header-left .ak-site_branding.white-logo {
  display: none;
}
.ak-site_header.style-2.type2.ak-gescout_sticky .ak-main-header-right .btn-wrapper .button-container span {
  color: var(--black-color);
}
.ak-site_header.style-2.type2.ak-gescout_sticky .ak-nav .ak-nav_list.typle2 a {
  color: var(--black-color);
}
.ak-site_header.style-2.type2.ak-gescout_sticky .ak-nav .ak-nav_list.typle2 a:hover {
  color: var(--primary-color);
}

/*--------------------------------------------------------------
22. Blog
----------------------------------------------------------------*/
.dark .blog-card {
  transition: all 0.4s ease-in-out;
}
.dark .blog-card:hover {
  background-color: #ff4a23;
}
.dark .blog-card:hover .blog-info .blog-card-btn {
  border-color: #ffffff;
}
.dark .blog-card:hover .blog-info .blog-card-btn i {
  color: #ffffff;
}
.dark .blog-card:hover .blog-info .blog-heading .meta-info .admin-tag,
.dark .blog-card:hover .blog-info .blog-heading .meta-info .date-tag {
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.dark .blog-card:hover .blog-info .blog-heading .blog-title {
  color: #ffffff;
}
.dark .blog-card:hover .blog-info .blog-heading .blog-description {
  color: #ffffff;
}
.dark .core-features-swiper-controller .core-features-navigation .core-features-button-prev,
.dark .core-features-swiper-controller .core-features-navigation .core-features-button-next {
  border: 1px solid #ffffff;
}
.dark .core-features-swiper-controller .core-features-navigation .core-features-button-prev svg path,
.dark .core-features-swiper-controller .core-features-navigation .core-features-button-next svg path {
  fill: #ffffff;
}
.dark .news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev,
.dark .news-blog-swiper-controller .news-blog-navigation .news-blog-button-next {
  border: 1px solid var(--black-color);
}

.blogs-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.blog-card {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
  transform-origin: center;
}
.blog-card .blog-image {
  max-width: 280px;
  height: 280px;
  width: 100%;
  overflow: hidden;
  position: relative;
}
.blog-card .blog-image img {
  position: absolute;
  top: -30%;
  width: 100%;
  height: 130%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog-card .blog-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
  width: 100%;
}
.blog-card .blog-info .blog-heading {
  max-width: 638px;
  width: 100%;
  margin: 30px 0;
}
.blog-card .blog-info .blog-heading .meta-info {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 31px;
  margin-bottom: 21px;
}
.blog-card .blog-info .blog-heading .meta-info .admin-tag,
.blog-card .blog-info .blog-heading .meta-info .date-tag {
  border-radius: 50px;
  border: 1px solid var(--border-color);
  padding: 2px 15px 0px 15px;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.5s ease-in-out;
}
.blog-card .blog-info .blog-heading .blog-title {
  font-size: 32px;
  margin-bottom: 25px;
  color: var(--black-color);
  line-height: 130%;
}
.blog-card .blog-info .blog-heading .blog-description {
  font-size: 16px;
}
.blog-card .blog-info .blog-card-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid var(--primary-color);
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease-in-out;
}
.blog-card .blog-info .blog-card-btn:hover {
  background-color: #ffffff;
  border: 1px solid transparent;
}
.blog-card .blog-info .blog-card-btn i {
  position: absolute;
  font-size: larger;
  line-height: 30%;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  color: #ff4a23;
}
.blog-card .blog-info .blog-card-btn i:nth-of-type(1) {
  transform: translate(0, 0);
  opacity: 1;
}
.blog-card .blog-info .blog-card-btn i:nth-of-type(2) {
  transform: translate(-40px, 60px);
  opacity: 0;
}
.blog-card .blog-info .blog-card-btn:hover i:nth-of-type(1) {
  transform: translate(40px, -60px);
  opacity: 0;
  color: #101010;
}
.blog-card .blog-info .blog-card-btn:hover i:nth-of-type(2) {
  transform: translate(0, 0);
  opacity: 1;
  color: #101010;
}
.blog-card:hover {
  margin: 0 20px;
  background-color: #101010;
}
.blog-card:hover .blog-info .blog-heading .meta-info .admin-tag,
.blog-card:hover .blog-info .blog-heading .meta-info .date-tag {
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.blog-card:hover .blog-info .blog-heading .blog-title {
  color: #ffffff;
}
.blog-card:hover .blog-info .blog-heading .blog-description {
  color: #ffffff;
}

.news-blog-card {
  background-color: var(--gray-bg);
  padding: 65px 40px;
  display: inline-block;
  position: relative;
  overflow: hidden;
}
.news-blog-card .news-blog-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  opacity: 0;
  transition: all 0.5s ease;
  z-index: -1;
}
.news-blog-card .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(16, 16, 16, 0);
  transition: background-color 0.5s ease;
  z-index: 0;
}
.news-blog-card:hover {
  background: transparent;
}
.news-blog-card:hover .news-blog-bg-img {
  opacity: 0.8;
}
.news-blog-card:hover .overlay {
  background-color: rgba(16, 16, 16, 0.75);
}
.news-blog-card:hover .news-blog-title,
.news-blog-card:hover .news-blog-desp,
.news-blog-card:hover .more-btn {
  color: #ffffff;
}
.news-blog-card:hover .more-btn::after {
  background-color: #ffffff;
}
.news-blog-card .news-blog-title {
  font-size: 22px;
  font-weight: 600;
  line-height: 150%;
  margin-bottom: 15px;
}
.news-blog-card .news-blog-desp {
  font-size: 16px;
  margin-bottom: 45px;
}
.news-blog-card .news-blog-title,
.news-blog-card .news-blog-desp,
.news-blog-card .more-btn {
  position: relative;
  z-index: 2;
}

.news-blog-swiper-controller {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.news-blog-swiper-controller .news-blog-scrollbar {
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  position: absolute;
  top: 50%;
}
.news-blog-swiper-controller .news-blog-navigation {
  display: flex;
  gap: 25px;
  flex-shrink: 0;
  z-index: 111;
  background-color: var(--white-color);
  transition: all 0.5s;
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev,
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-next {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 1px solid var(--border-color);
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev svg,
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-next svg {
  transition: all 0.3s ease-in-out;
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev svg path,
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-next svg path {
  fill: var(--black-color);
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev:hover svg {
  margin-right: 10px;
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-prev:hover svg path {
  fill: #ff4a23;
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-next:hover svg {
  margin-left: 10px;
}
.news-blog-swiper-controller .news-blog-navigation .news-blog-button-next:hover svg path {
  fill: #ff4a23;
}

.core-features-swiper-controller {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.core-features-swiper-controller .core-features-scrollbar {
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  position: absolute;
  top: 50%;
}
.core-features-swiper-controller .core-features-navigation {
  display: flex;
  gap: 25px;
  flex-shrink: 0;
  z-index: 111;
  background-color: var(--white-color);
}
.core-features-swiper-controller .core-features-navigation .core-features-button-prev,
.core-features-swiper-controller .core-features-navigation .core-features-button-next {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  background-color: transparent;
}
.core-features-swiper-controller .core-features-navigation .core-features-button-prev svg,
.core-features-swiper-controller .core-features-navigation .core-features-button-next svg {
  transition: all 0.3s ease-in-out;
}
.core-features-swiper-controller .core-features-navigation .core-features-button-prev:hover {
  border: 1px solid var(--primary-color);
}
.core-features-swiper-controller .core-features-navigation .core-features-button-prev:hover svg {
  margin-right: 10px;
}
.core-features-swiper-controller .core-features-navigation .core-features-button-prev:hover svg path {
  fill: var(--primary-color);
}
.core-features-swiper-controller .core-features-navigation .core-features-button-next:hover {
  border: 1px solid var(--primary-color);
}
.core-features-swiper-controller .core-features-navigation .core-features-button-next:hover svg {
  margin-left: 10px;
}
.core-features-swiper-controller .core-features-navigation .core-features-button-next:hover svg path {
  fill: var(--primary-color);
}

.blogs-details-wapper {
  max-width: 930px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.blogs-details-wapper .blogs-details-main-title {
  font-size: 45px;
  line-height: 125%;
}
.blogs-details-wapper .blogs-details-desp-text {
  line-height: 160%;
  font-size: 16px;
}
.blogs-details-wapper .blogs-details-quote-text {
  font-style: italic;
  position: relative;
  min-height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blogs-details-wapper .blogs-details-quote-text img {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 12;
}
.blogs-details-wapper .blogs-details-quote-text p {
  position: relative;
  z-index: 13;
  padding: 80px;
  color: var(--black-color);
}

@media screen and (max-width: 991px) {
  .blogs-content {
    flex-direction: row;
    flex-wrap: wrap;
  }
  .blogs-content > * {
    max-width: 50%;
    min-width: 35%;
    width: 100%;
    flex: 1;
  }
  .blog-card {
    flex-wrap: wrap;
  }
  .blog-card:hover {
    margin: 0;
  }
  .blog-card .blog-image {
    max-width: 100%;
  }
  .blog-card .blog-info {
    flex-wrap: wrap;
  }
  .blog-card .blog-info .blog-heading {
    margin: 0;
  }
  .blog-card .blog-info .blog-heading .blog-title {
    font-size: 26px;
    margin-bottom: 20px;
  }
}
@media screen and (max-width: 767px) {
  .blogs-details-wapper .blogs-details-main-title {
    font-size: 34px;
  }
  .blogs-details-wapper .blogs-details-quote-text p {
    padding: 40px;
  }
  .blogs-content {
    flex-direction: column;
  }
  .blogs-content > * {
    max-width: 100%;
    min-width: 35%;
    width: 100%;
  }
  .blog-card .blog-info .blog-heading .blog-title {
    font-size: 24px;
  }
  .news-blog-card {
    padding: 45px 25px;
  }
}
/*--------------------------------------------------------------
  23. Accordion
----------------------------------------------------------------*/
.ak-accordion-tab {
  display: none;
}

.ak-accordion {
  display: flex;
  flex-direction: column;
  gap: 45px;
}
.ak-accordion .ak-accordion-item:first-child .ak-accordion-tab {
  display: block;
}

.ak-accordion-item .ak-accordion-title-content {
  height: 53px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
}
.ak-accordion-item .ak-accordion-title-content:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  bottom: -5px;
}
.ak-accordion-item .ak-accordion-title-content .ak-accordion-title {
  font-size: 22px;
  font-weight: 400;
  color: var(--black-color);
  width: 100%;
}
.ak-accordion-item .ak-accordion-title-content span {
  align-self: flex-start;
  transform: rotate(180deg);
}
.ak-accordion-item .ak-accordion-title-content.active .accordion-icon {
  border: 1px solid #ff4a23;
}
.ak-accordion-item .ak-accordion-title-content.active .accordion-icon svg path {
  fill: #ff4a23;
}
.ak-accordion-item .ak-accordion-title-content.active span {
  transform: rotate(0deg);
}
.ak-accordion-item .ak-accordion-title-content .accordion-icon {
  border: 1px solid var(--black-color);
  border-radius: 50%;
  width: 26px;
  height: 26px;
  text-align: center;
}
.ak-accordion-item .ak-accordion-title-content .accordion-icon svg path {
  fill: var(--black-color);
}
.ak-accordion-item .ak-accordion-tab {
  padding-top: 30px;
}
.ak-accordion-item.style-2 {
  padding: 23px;
}
.ak-accordion-item.style-2 .ak-accordion-title-content {
  height: auto;
}
.ak-accordion-item.style-2 .ak-accordion-title-content:after {
  display: none;
}
.ak-accordion-item.style-3 .ak-accordion-title-content:after {
  background-color: #063232;
}
.ak-accordion-item.style-3 .ak-accordion-title-content .ak-accordion-title {
  color: #ffffff;
}
.ak-accordion-item.style-3 .ak-accordion-tab {
  padding-bottom: 50px;
}

.faq-wapper {
  display: flex;
  gap: 72px;
  align-items: center;
}
.faq-wapper .faq-img-content {
  max-width: 870px;
  width: 100%;
}
.faq-wapper .faq-accordion {
  max-width: 677px;
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .faq-wapper {
    flex-direction: column;
    gap: 50px;
    align-items: center;
  }
  .faq-wapper .faq-img-content .image-hov-one {
    max-height: 400px;
    height: 100%;
  }
}
/*--------------------------------------------------------------
  24. Cta
----------------------------------------------------------------*/
.dark .services-details-cta {
  background-color: #ff4a23;
}
.dark .services-details-cta-title .highlight {
  color: #101010;
}
.dark .services-details-cta .services-bg-start {
  opacity: 0.1;
}
.dark .services-details-cta .dot-top-left,
.dark .services-details-cta .dot-top-right,
.dark .services-details-cta .dot-bottom-left,
.dark .services-details-cta .dot-bottom-right {
  background-color: #ffffff;
}

.services-details-cta {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  flex-direction: column;
  height: 350px;
  position: relative;
  background-color: var(--gray-bg);
  margin-bottom: 100px;
}
.services-details-cta .dot-top-left,
.services-details-cta .dot-top-right,
.services-details-cta .dot-bottom-left,
.services-details-cta .dot-bottom-right {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #ff4a23;
  z-index: 2;
}
.services-details-cta .dot-top-left {
  top: -4px;
  left: -4px;
}
.services-details-cta .dot-top-right {
  top: -4px;
  right: -4px;
}
.services-details-cta .dot-bottom-left {
  bottom: -4px;
  left: -4px;
}
.services-details-cta .dot-bottom-right {
  bottom: -4px;
  right: -4px;
}
.services-details-cta .services-bg-start {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5%;
}
.services-details-cta-content {
  max-width: 740px;
  width: 100%;
}
.services-details-cta-wapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
  width: 100%;
}
.services-details-cta-title {
  font-size: 55px;
  line-height: 120%;
}
.services-details-cta-title .highlight {
  text-decoration: underline;
  color: #ff4a23;
  font-weight: 400;
  font-style: italic;
  font-family: var(--secondary-font-family);
  position: relative;
}
.services-details-cta-title .highlight.text-underlines::after {
  content: "";
  position: absolute;
  left: 0%;
  bottom: 2px;
  width: 100%;
  height: 2px;
  background-color: #ff4a23;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 1s ease-in-out, opacity 1s ease-in-out;
  opacity: 0;
}
.services-details-cta-title .highlight.text-underlines.active::after {
  transition-delay: 1.2s;
  transform: scaleX(1);
  opacity: 1;
}
.services-details-cta-btn {
  position: absolute;
  bottom: -35%;
  padding: 15px;
  background-color: var(--white-color);
  border-radius: 50%;
}
.services-details-cta-btn .circle-btn {
  border: 1px solid var(--black-color);
  color: var(--black-color);
}

@media screen and (max-width: 768px) {
  .services-details-cta-title {
    font-size: 36px;
  }
}
/*--------------------------------------------------------------
25. Pricing Calculator
----------------------------------------------------------------*/
.package-content {
  display: flex;
  gap: 30px;
}
.package-content.style3 {
  gap: 0;
}
.package-content .package-one,
.package-content .package-two,
.package-content .package-three {
  flex-grow: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  max-width: 440px;
  width: 100%;
}
.package-content .package-one.style2,
.package-content .package-two.style2,
.package-content .package-three.style2 {
  transition: all 0.3s ease;
}
.package-content .package-one.style2.active,
.package-content .package-two.style2.active,
.package-content .package-three.style2.active {
  background-color: black;
  cursor: pointer;
  color: #ffffff;
}
.package-content .package-one.style2.active .common-btn.style-3,
.package-content .package-two.style2.active .common-btn.style-3,
.package-content .package-three.style2.active .common-btn.style-3 {
  background-color: #ff4a23;
  border: 1px solid #101010;
  color: #ffffff;
}
.package-content .package-one.style2.active .package-head-info .title,
.package-content .package-two.style2.active .package-head-info .title,
.package-content .package-three.style2.active .package-head-info .title {
  color: #ffffff;
}
.package-content .package-one.style2.active .package-price-info .price,
.package-content .package-two.style2.active .package-price-info .price,
.package-content .package-three.style2.active .package-price-info .price {
  color: #ffffff;
}
.package-content .package-one.style2.active .package-list li i,
.package-content .package-two.style2.active .package-list li i,
.package-content .package-three.style2.active .package-list li i {
  color: #ff4a23;
}
.package-content .package-one.style3,
.package-content .package-two.style3,
.package-content .package-three.style3 {
  border: 1px solid var(--border-color);
}
.package-content .package-one.style3 .package-btn-content,
.package-content .package-two.style3 .package-btn-content,
.package-content .package-three.style3 .package-btn-content {
  padding: 20px 20px 13px 20px;
  width: 100%;
  border-top: 1px solid var(--border-color);
}
.package-content .package-one.style3 .package-btn-content:hover,
.package-content .package-two.style3 .package-btn-content:hover,
.package-content .package-three.style3 .package-btn-content:hover {
  background-color: #ff4a23;
}
.package-content .package-one.style3 .package-btn-content:hover .package-btn,
.package-content .package-two.style3 .package-btn-content:hover .package-btn,
.package-content .package-three.style3 .package-btn-content:hover .package-btn {
  color: #ffffff;
}
.package-content .package-one.style3 .package-btn-content:hover .package-btn::after,
.package-content .package-two.style3 .package-btn-content:hover .package-btn::after,
.package-content .package-three.style3 .package-btn-content:hover .package-btn::after {
  background-color: #ffffff;
}
.package-content .package-one.style3 .package-btn-content:hover .package-btn .svg-icon path,
.package-content .package-two.style3 .package-btn-content:hover .package-btn .svg-icon path,
.package-content .package-three.style3 .package-btn-content:hover .package-btn .svg-icon path {
  stroke: #ffffff;
}
.package-content .package-one.style3 .package-btn-content:hover .package-btn .svg-icon i,
.package-content .package-two.style3 .package-btn-content:hover .package-btn .svg-icon i,
.package-content .package-three.style3 .package-btn-content:hover .package-btn .svg-icon i {
  color: #ffffff;
}
.package-content .package-one.style3 .package-btn,
.package-content .package-two.style3 .package-btn,
.package-content .package-three.style3 .package-btn {
  position: relative;
  display: inline-flex;
  color: var(--black-color);
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 6px;
  flex-shrink: 0;
}
.package-content .package-one.style3 .package-btn .primary-icon-anim,
.package-content .package-two.style3 .package-btn .primary-icon-anim,
.package-content .package-three.style3 .package-btn .primary-icon-anim {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 24px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.package-content .package-one.style3 .package-btn .primary-icon-anim i,
.package-content .package-two.style3 .package-btn .primary-icon-anim i,
.package-content .package-three.style3 .package-btn .primary-icon-anim i {
  position: absolute;
  font-size: larger;
  line-height: 30%;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  color: #ffffff;
  font-size: 16px;
}
.package-content .package-one.style3 .package-btn .primary-icon-anim i:nth-of-type(1),
.package-content .package-two.style3 .package-btn .primary-icon-anim i:nth-of-type(1),
.package-content .package-three.style3 .package-btn .primary-icon-anim i:nth-of-type(1) {
  transform: translate(0, 0);
  opacity: 1;
}
.package-content .package-one.style3 .package-btn .primary-icon-anim i:nth-of-type(2),
.package-content .package-two.style3 .package-btn .primary-icon-anim i:nth-of-type(2),
.package-content .package-three.style3 .package-btn .primary-icon-anim i:nth-of-type(2) {
  transform: translate(-40px, 60px);
  opacity: 0;
}
.package-content .package-one.style3 .package-btn::after,
.package-content .package-two.style3 .package-btn::after,
.package-content .package-three.style3 .package-btn::after {
  content: "";
  position: absolute;
  display: inline;
  width: 0%;
  height: 1px;
  left: 0;
  bottom: 5px;
  opacity: 1;
  transition: all 0.3s;
  background-color: var(--black-color);
}
.package-content .package-one.style3 .package-btn:hover,
.package-content .package-two.style3 .package-btn:hover,
.package-content .package-three.style3 .package-btn:hover {
  color: var(--black-color);
}
.package-content .package-one.style3 .package-btn:hover::after,
.package-content .package-two.style3 .package-btn:hover::after,
.package-content .package-three.style3 .package-btn:hover::after {
  width: 70%;
}
.package-content .package-one.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(1),
.package-content .package-two.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(1),
.package-content .package-three.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(1) {
  transform: translate(40px, -60px);
  opacity: 0;
  color: #ffffff;
}
.package-content .package-one.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(2),
.package-content .package-two.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(2),
.package-content .package-three.style3 .package-btn:hover .primary-icon-anim i:nth-of-type(2) {
  transform: translate(0, 0);
  opacity: 1;
  color: #ffffff;
}
.package-content .package-one .package-head-info,
.package-content .package-two .package-head-info,
.package-content .package-three .package-head-info {
  position: relative;
  margin: 60px 0;
}
.package-content .package-one .package-head-info .title,
.package-content .package-two .package-head-info .title,
.package-content .package-three .package-head-info .title {
  font-size: 35px;
  color: var(--black-color);
}
.package-content .package-one .package-head-info .duration,
.package-content .package-two .package-head-info .duration,
.package-content .package-three .package-head-info .duration {
  font-size: 16px;
}
.package-content .package-one .package-price-info,
.package-content .package-two .package-price-info,
.package-content .package-three .package-price-info {
  margin: 60px 0 40px 0;
}
.package-content .package-one .package-price-info .price,
.package-content .package-two .package-price-info .price,
.package-content .package-three .package-price-info .price {
  color: var(--black-color);
  font-size: 45px;
}
.package-content .package-one .package-price-info .price .max-price,
.package-content .package-two .package-price-info .price .max-price,
.package-content .package-three .package-price-info .price .max-price {
  font-size: 16px;
  font-weight: 400;
  opacity: 0.8;
}
.package-content .package-one .package-list,
.package-content .package-two .package-list,
.package-content .package-three .package-list {
  text-align: start;
  max-width: 320px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.package-content .package-one .package-list li,
.package-content .package-two .package-list li,
.package-content .package-three .package-list li {
  display: flex;
  gap: 10px;
}
.package-content .package-one .package-list li i,
.package-content .package-two .package-list li i,
.package-content .package-three .package-list li i {
  color: #ff4a23;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-color: rgba(193, 193, 193, 0.0823529412);
  border-radius: 50%;
}
.package-content .package-one .common-btn,
.package-content .package-two .common-btn,
.package-content .package-three .common-btn {
  margin-bottom: 50px;
}

@media screen and (max-width: 1199px) {
  .package-content .package-one.style2,
  .package-content .package-two.style2,
  .package-content .package-three.style2 {
    max-width: 420px;
    width: 100%;
  }
}
@media screen and (max-width: 991px) {
  .package-content {
    flex-direction: column;
    align-items: center;
  }
}
/*--------------------------------------------------------------
26. Portfolio
----------------------------------------------------------------*/
.portfolio-content .portfolio-title-content {
  font-size: 55px;
  text-align: center;
  margin-top: 41px;
}
.portfolio-content .portfolio-title-content .highlight {
  color: var(--primary-color);
  font-style: italic;
  font-weight: 400;
  font-family: var(--secondary-font-family);
}
.portfolio-content .portfolio-title-content .highlight.style-2 {
  font-size: 95px;
  line-height: 100%;
}
.portfolio-content .portfolio-content-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 134px;
}
.portfolio-content .portfolio-content-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 94px;
}

.portfolio-card.style-1 {
  position: relative;
  overflow: hidden;
  display: inline-block;
  transition: all 0.5s ease;
}
.portfolio-card.style-1 .portfolio-img {
  width: 100%;
  height: 100%;
}
.portfolio-card.style-1 .portfolio-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.portfolio-card.style-1 .portfolio-info {
  position: absolute;
  left: 50px;
  right: 30px;
  bottom: 0px;
  opacity: 0;
  background-color: transparent;
  transition: all 0.3s ease-in-out;
}
.portfolio-card.style-1 .portfolio-info .portfolio-subtitle {
  display: inline-flex;
  padding: 2px 15px 0px 15px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 50px;
  color: #ffffff;
  border: 1px solid #ffffff;
  margin-bottom: 18px;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: space-between;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text .portfolio-title {
  color: #ffffff;
  font-size: 35px;
  line-height: 130%;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text .portfolio-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid #ffffff;
  background-color: transparent;
  transition: background-color 0.3s ease-in-out;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text .portfolio-btn i {
  transition: all 0.3s ease-in-out;
  line-height: normal;
  color: #ffffff;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text:hover .portfolio-btn {
  background-color: #ffffff;
}
.portfolio-card.style-1 .portfolio-info .portfolio-text:hover .portfolio-btn i {
  color: #101010;
}
.portfolio-card.style-1:hover > .portfolio-info {
  bottom: 50px;
  opacity: 1;
}

.portfolio-card {
  display: inline-block;
}
.portfolio-card .portfolio-top-img {
  margin-bottom: 30px;
}
.portfolio-card .portfolio-content {
  width: 100%;
  display: flex;
  gap: 40px;
  justify-content: space-between;
  padding: 0 15px;
}
.portfolio-card .portfolio-content .portfolio-title {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
  transition: color 0.3s ease;
}
.portfolio-card .portfolio-content .portfolio-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid #ff4a23;
  transition: background-color 0.4s ease;
}
.portfolio-card .portfolio-content .portfolio-btn i {
  transition: transform 0.4s;
  line-height: normal;
  color: #ff4a23;
}
.portfolio-card:hover .portfolio-content .portfolio-title {
  color: #ff4a23;
}
.portfolio-card:hover .portfolio-btn {
  background-color: var(--black-color);
  border: 1px solid transparent;
}
.portfolio-card:hover .portfolio-btn i {
  color: var(--white-color);
}

.portfolio-details-container {
  max-width: 930px;
}

.portfolio-main-img {
  position: relative;
}
.portfolio-main-img .image-content {
  max-width: 930px;
  width: 100%;
  padding: 50px;
  display: flex;
  position: absolute;
  bottom: 50px;
  background-color: var(--white-color);
}
.portfolio-main-img .image-content .image-content-title {
  max-width: 353px;
  width: 100%;
  font-size: 28px;
  line-height: 130%;
}
.portfolio-main-img .image-content .image-content-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}
.portfolio-main-img .image-content .image-content-box .image-info-title {
  font-size: 22px;
  font-weight: 500;
  line-height: 150%;
}
.portfolio-main-img .image-content .image-content-box .image-info-desp {
  font-size: 18px;
}

.portfolio-details-box {
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.portfolio-details-box .pd-desp {
  font-size: 16px;
}

@media screen and (max-width: 991px) {
  .portfolio-card .portfolio-content {
    flex-direction: column;
  }
  .portfolio-content .portfolio-title-content {
    font-size: 34px;
    text-align: center;
    margin-top: 41px;
  }
  .portfolio-content .portfolio-title-content .highlight.style-2 {
    font-size: 45px;
  }
  .portfolio-content .portfolio-content-top {
    gap: 50px;
  }
  .portfolio-content .portfolio-content-bottom {
    gap: 50px;
  }
  .portfolio-card.style-1 {
    height: 400px;
    width: 100%;
  }
  .portfolio-card.style-1 .portfolio-info {
    left: 15px;
    right: 15px;
  }
  .portfolio-card.style-1 .portfolio-info .portfolio-text.style-1 .portfolio-title {
    font-size: 24px;
  }
  .portfolio-main-img {
    flex-direction: column;
  }
  .portfolio-main-img .td-main-img,
  .portfolio-main-img .pd-main-img {
    height: 400px;
    -o-object-fit: cover;
       object-fit: cover;
  }
  .portfolio-main-img .image-content {
    position: relative;
    flex-direction: column;
    padding: 15px;
    bottom: 0;
    gap: 30px;
  }
  .portfolio-main-img .image-content .image-content-box {
    justify-content: start;
  }
}
@media screen and (max-width: 767px) {
  .portfolio-content .portfolio-title-content {
    font-size: 34px;
    text-align: center;
    margin-top: 41px;
  }
  .portfolio-content .portfolio-title-content .highlight.style-2 {
    font-size: 45px;
  }
  .portfolio-content .portfolio-content-top {
    flex-wrap: wrap;
  }
  .portfolio-content .portfolio-content-bottom {
    flex-wrap: wrap;
  }
}
@media screen and (max-width: 575px) {
  .portfolio-main-img .image-content .image-content-box .image-info {
    width: 100%;
  }
}
/*--------------------------------------------------------------
  27. Awards
----------------------------------------------------------------*/
.awards-box {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.awards-box .awards-hover-image {
  position: absolute;
  left: -100px;
}
.awards-box .awards-hover-image img {
  min-width: 400px;
}

.awards-list {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.awards-card {
  max-width: 1030px;
  width: 100%;
  padding: 12px 0px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  justify-content: space-between;
  align-self: flex-end;
  cursor: pointer;
}
.awards-card .awards-hover {
  display: none;
}
.awards-card .awards-title {
  transition: font-size 0.5s ease;
}
.awards-card .awards-img img {
  transition: transform 1s ease;
  transform: rotate3d(0, 0, 0, 0deg);
}
.awards-card:hover .awards-title {
  font-size: 24px;
}
.awards-card:hover .awards-img img {
  transform: rotate3d(0, 1, 0, 180deg);
}

@media screen and (max-width: 767px) {
  .awards-list {
    gap: 30px;
  }
  .awards-card {
    flex-direction: column;
    max-width: 100%;
    align-items: self-start;
    gap: 15px;
  }
  .awards-box {
    position: relative;
  }
  .awards-box .awards-hover-image {
    display: none;
    position: absolute;
    left: -10%;
    top: 50%;
    transform: translate(0, -50%);
  }
}
/*--------------------------------------------------------------
28. Newsletter
----------------------------------------------------------------*/
.dark .newsletter-content .newsletter-form .newsletter-input {
  background-image: linear-gradient(black, black), linear-gradient(to right, #ff4a23 0%, #ff4a23 25%, #101010 50%, #ff4a23 75%, #ff4a23 100%);
}
.dark .newsletter-content .newsletter-form .newsletter-input:focus {
  background-image: linear-gradient(black, black), linear-gradient(to right, #ff4a23 0%, #101010 25%, #ff4a23 50%, #101010 75%, #ff4a23 100%);
}

.newsletter-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.newsletter-content.style-2 {
  flex-direction: row;
  justify-content: space-between;
}
.newsletter-content.style-2 .newsletter-title-content .newsletter-title {
  max-width: 685px;
  width: 100%;
  text-align: start;
  margin: 0;
}
.newsletter-content.style-2 .newsletter-title-content .newsletter-title .highlight.text-underlines::after {
  bottom: 6px;
}
.newsletter-content.style-2 .newsletter-title {
  font-size: 55px;
}
.newsletter-content.style-2 .newsletter-form {
  top: auto;
}
.newsletter-content .newsletter-title {
  max-width: 941px;
  width: 100%;
  font-size: 70px;
  font-weight: 600;
  line-height: 105%;
  text-align: center;
  margin: 24px 0;
}
.newsletter-content .newsletter-title .highlight {
  font-style: italic;
  font-family: var(--secondary-font-family);
  font-weight: 400;
  color: #ff4a23;
  position: relative;
}
.newsletter-content .newsletter-title .highlight.text-underlines::after {
  content: "";
  position: absolute;
  left: 0%;
  bottom: 22px;
  width: 94%;
  height: 1px;
  background-color: #ff4a23;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 1s ease-in-out, opacity 1s ease-in-out;
  opacity: 0;
}
.newsletter-content .newsletter-title .highlight.text-underlines.active::after {
  transition-delay: 1.2s;
  transform: scaleX(1);
  opacity: 1;
}
.newsletter-content .newsletter-form {
  max-width: 520px;
  width: 100%;
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  top: 28px;
}
.newsletter-content .newsletter-form .newsletter-input {
  width: 100%;
  height: 100%;
  padding: 20px 30px;
  outline: none;
  background: transparent;
  font-size: 18px;
  border: 1px solid transparent;
  border-radius: 50px;
  overflow: hidden;
  transition: background-image 0.5s ease;
  background-image: linear-gradient(white, white), linear-gradient(to right, #ff4a23 0%, #ff4a23 25%, #101010 50%, #ff4a23 75%, #ff4a23 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}
.newsletter-content .newsletter-form .newsletter-input.style-2 {
  transition: border 0.5s ease;
  background-image: none;
  border: 1px solid var(--border-color);
}
.newsletter-content .newsletter-form .newsletter-input.style-2:focus {
  border-color: var(--balck-color);
}
.newsletter-content .newsletter-form .newsletter-input:focus {
  background-image: linear-gradient(white, white), linear-gradient(to right, #ff4a23 0%, #101010 25%, #ff4a23 50%, #101010 75%, #ff4a23 100%);
}
.newsletter-content .newsletter-form .newsletter-btn {
  position: absolute;
  right: 32px;
  border: none;
  background-color: transparent;
  font-weight: 500;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
.newsletter-content .newsletter-form .newsletter-btn .newbtn-text {
  font-size: 18px;
  margin-top: 3px;
}
.newsletter-content .newsletter-form .newsletter-btn:hover .primary-icon-anim i:nth-of-type(1) {
  transform: translate(40px, -60px);
  opacity: 0;
}
.newsletter-content .newsletter-form .newsletter-btn:hover .primary-icon-anim i:nth-of-type(2) {
  transform: translate(0, 0);
  opacity: 1;
}

.newsletter-wapper.active .cta-form-border .b-top-left,
.newsletter-wapper.active .cta-form-border .b-top-right,
.newsletter-wapper.active .cta-form-border .b-bottom-right,
.newsletter-wapper.active .cta-form-border .b-bottom-left {
  transition: all 0.5s ease-in-out;
  opacity: 1;
}
.newsletter-wapper.active .newsletter-content .newsletter-anim .newsletter-title .highlight.text-underlines::after {
  transform: scaleX(1);
  opacity: 1;
}

@media screen and (max-width: 1200px) {
  .newsletter-content .newsletter-title {
    font-size: 60px;
    max-width: 800px;
  }
  .newsletter-content .newsletter-form {
    width: 480px;
  }
}
@media screen and (max-width: 992px) {
  .newsletter-content.style-2 {
    gap: 30px;
    flex-wrap: wrap;
  }
  .newsletter-content.style-2 .newsletter-title {
    font-size: 50px;
  }
  .newsletter-content .newsletter-title {
    font-size: 50px;
    max-width: 700px;
  }
  .newsletter-content .newsletter-form {
    width: 400px;
    height: 65px;
  }
  .newsletter-content .newsletter-form .newsletter-input {
    font-size: 16px;
    padding: 15px 25px;
  }
}
@media screen and (max-width: 768px) {
  .newsletter-content.style-2 .newsletter-title {
    font-size: 36px;
  }
  .newsletter-content .newsletter-title {
    font-size: 42px;
    max-width: 600px;
  }
  .newsletter-content .newsletter-form {
    width: 90%;
    height: 60px;
  }
  .newsletter-content .newsletter-form .newsletter-input {
    font-size: 14px;
    padding: 12px 20px;
  }
}
@media screen and (max-width: 576px) {
  .newsletter-content .newsletter-title {
    font-size: 36px;
    max-width: 500px;
    line-height: 108%;
  }
  .newsletter-content .newsletter-title .highlight.text-underlines::after {
    bottom: 10px;
  }
  .newsletter-content .newsletter-form {
    width: 100%;
    height: 55px;
  }
  .newsletter-content .newsletter-form .newsletter-input {
    font-size: 16px;
    padding: 10px 15px;
  }
  .newsletter-content .newsletter-btn {
    right: 10px !important;
  }
  .newsletter-content .newsletter-btn .newbtn-text {
    font-size: 16px !important;
  }
}
/*--------------------------------------------------------------
20. Team
----------------------------------------------------------------*/
.news-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  overflow: hidden;
}
.news-card .news-img-content {
  position: relative;
  overflow: hidden;
  max-height: 350px;
  width: 100%;
}
.news-card .news-img-content .news-img-top {
  position: relative;
  transition: transform 500ms;
  transform: perspective(0) rotateX(0) rotateY(0) scaleX(1) scaleY(1);
  transform-origin: center center;
  overflow: hidden;
}
.news-card .news-img-content .news-img-top img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.news-card .news-body {
  flex: 1 1 auto;
  margin: 45px 0px 0px 20px;
}
.news-card .news-body .news-title {
  margin-bottom: 18px;
}
.news-card .news-body .news-title,
.news-card .news-body .news-text {
  transition: all 0.3s;
  line-height: 165%;
}
.news-card .news-body .news-text {
  font-size: 16px;
  margin-bottom: 48px;
}
.news-card:hover .news-body .news-title {
  color: #ff4a23;
}
.news-card:hover .news-img-content .news-img-top {
  transform: perspective(600px) rotateX(0.06deg) rotateY(0) scaleX(1.1) scaleY(1.1);
}

.dark .news-swiper-controller .news-logs-navigation .news-logs-button-prev,
.dark .news-swiper-controller .news-logs-navigation .news-logs-button-next {
  border: 1px solid #ffffff;
}
.dark .news-swiper-controller .news-logs-navigation .news-logs-button-prev svg path,
.dark .news-swiper-controller .news-logs-navigation .news-logs-button-next svg path {
  fill: #ffffff;
}

.news-swiper-controller {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.news-swiper-controller .news-logs-scrollbar {
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  position: absolute;
  top: 50%;
}
.news-swiper-controller .news-logs-navigation {
  display: flex;
  gap: 25px;
  flex-shrink: 0;
  z-index: 111;
  background-color: var(--white-color);
  transition: all 0.5s;
}
.news-swiper-controller .news-logs-navigation .news-logs-button-prev,
.news-swiper-controller .news-logs-navigation .news-logs-button-next {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 1px solid var(--border-color);
}
.news-swiper-controller .news-logs-navigation .news-logs-button-prev svg,
.news-swiper-controller .news-logs-navigation .news-logs-button-next svg {
  transition: all 0.3s ease-in-out;
}
.news-swiper-controller .news-logs-navigation .news-logs-button-prev:hover {
  border: 1px solid var(--primary-color);
}
.news-swiper-controller .news-logs-navigation .news-logs-button-prev:hover svg {
  margin-right: 10px;
}
.news-swiper-controller .news-logs-navigation .news-logs-button-prev:hover svg path {
  fill: var(--primary-color);
}
.news-swiper-controller .news-logs-navigation .news-logs-button-next:hover {
  border: 1px solid var(--primary-color);
}
.news-swiper-controller .news-logs-navigation .news-logs-button-next:hover svg {
  margin-left: 10px;
}
.news-swiper-controller .news-logs-navigation .news-logs-button-next:hover svg path {
  fill: var(--primary-color);
}

@media screen and (max-width: 768px) {
  .news-card {
    flex: 1 1 100%;
    margin: 45px 0px 0px 0px;
  }
  .news-card .news-body .news-text {
    margin-bottom: 10px;
  }
}
/*--------------------------------------------------------------
30. Progress Bar
----------------------------------------------------------------*/
.ak-skill-box {
  margin-bottom: 50px;
}
.ak-skill-box:last-child {
  margin-bottom: 0;
}
.ak-skill-box.type-2 .ak-skill-bar {
  background-color: #101010;
}
.ak-skill-box.type-2 .ak-skill-bar .ak-skill-fill {
  background-color: #ffffff;
}
.ak-skill-box.type-2 .ak-skill-text .ak-skill-title {
  color: #ffffff;
}
.ak-skill-box.type-2 .ak-skill-text .ak-skill-per {
  color: #ffffff;
}
.ak-skill-box .ak-skill-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.ak-skill-box .ak-skill-text .ak-skill-title {
  font-size: 16px;
  color: var(--heading-color);
  text-transform: uppercase;
}
.ak-skill-box .ak-skill-text .ak-skill-per {
  font-size: 16px;
  color: var(--black-color);
  font-weight: 400;
}
.ak-skill-box .ak-skill-bar {
  background-color: var(--border-color);
  height: 8px;
  width: 100%;
  overflow: hidden;
}
.ak-skill-box .ak-skill-bar .ak-skill-fill {
  background-color: var(--black-color);
  height: 100%;
  width: 0;
}

.progress-goal {
  display: flex;
}
.progress-goal > * {
  width: 50%;
}

.goal-circle-container {
  display: flex;
  gap: 40px;
  align-items: flex-end;
  justify-content: center;
}
.goal-circle-container .goal-circle {
  position: relative;
  width: 236px;
}
.goal-circle-container .goal-circle .goal-circle-overlay {
  width: 100%;
  height: 100%;
}
.goal-circle-container .goal-circle img {
  position: absolute;
  left: 50%;
  top: 0;
  transform: rotateY(0deg);
  transition: transform 0.5s ease, right 0.5s ease;
  transform-origin: center center;
}

@keyframes zigzag-animation {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@media screen and (max-width: 991px) {
  .progress-goal {
    flex-direction: column;
    gap: 50px;
  }
  .progress-goal > * {
    width: 100%;
  }
  .progress-goal .goal-shape {
    display: flex;
  }
}
/*--------------------------------------------------------------
31. Contact Form
----------------------------------------------------------------*/
.contact-form-box {
  max-width: 750px;
  width: 100%;
  position: relative;
  z-index: 111;
}

.dark .field-group .input-field {
  color: #101010;
}
.dark .field-group .input-field:focus ~ .input-label {
  display: block;
  position: absolute;
  top: 0;
  font-size: 18px;
  color: #000000;
}
.dark .field-group .input-field:focus {
  -o-border-image: linear-gradient(to right, #101010, #ff4a23);
     border-image: linear-gradient(to right, #101010, #ff4a23);
  border-image-slice: 1;
}
.dark .contact-form-container .contact-form {
  background-color: #ff4a23;
}
.dark .contact-cards .contact-card {
  background: radial-gradient(341.22% 40.56% at 50% 50%, #ff4a23 0%, rgba(255, 74, 35, 0.7) 100%);
}
.dark .contact-cards .contact-card .contact-icon {
  margin-bottom: 30px;
}
.dark .contact-cards .contact-card .contact-icon svg path {
  stroke: #ffffff;
}
.dark .contact-cards .contact-card .contact-icon.email svg path {
  fill: #ffffff;
  stroke: #ffffff;
}
.dark .contact-form-wapper.style-2 .contact-us-btn {
  background-color: #ff4a23;
  color: #ffffff;
}

.field-group {
  position: relative;
  padding-top: 30px;
  margin-bottom: 42px;
}
.field-group .input-field {
  width: 100%;
  outline: none;
  border: 0;
  border-bottom: 1px solid var(--body-color);
  padding: 5px 0px;
  background: transparent;
  font-size: 18px;
  color: #ffffff;
  transition: 0.3s ease;
}
.field-group .input-field::-moz-placeholder {
  color: transparent;
}
.field-group .input-field::placeholder {
  color: transparent;
}
.field-group .input-field:focus {
  -o-border-image: linear-gradient(to right, #ff4a23, #101010);
     border-image: linear-gradient(to right, #ff4a23, #101010);
  border-image-slice: 1;
}
.field-group .input-field:focus ~ .input-label {
  display: block;
  position: absolute;
  top: 0;
  font-size: 18px;
  color: #ff6b00;
}
.field-group .input-label {
  display: block;
  position: absolute;
  top: 0;
  margin: 0;
  font-size: 1em;
  color: #ffffff;
  text-transform: uppercase;
  transition: 0.3s ease;
}
.field-group.style-2 .input-field {
  color: #ffffff;
  border-bottom: 1px solid #ffffff;
}
.field-group.style-2 .input-field:focus ~ .input-label {
  display: block;
  position: absolute;
  top: 0;
  font-size: 18px;
  color: #ffffff;
}
.field-group.style-2 .input-field:focus {
  -o-border-image: linear-gradient(to right, #ffffff, #ff4a23);
     border-image: linear-gradient(to right, #ffffff, #ff4a23);
  border-image-slice: 1;
}

.input-field:-moz-placeholder-shown ~ .input-label {
  top: 20px;
  font-size: 18px;
}

.input-field:placeholder-shown ~ .input-label {
  top: 20px;
  font-size: 18px;
}

.contact-form-container {
  display: flex;
  gap: 59px;
}
@media (min-width: 1400px) {
  .contact-form-container {
    max-width: 1620px;
    margin-right: 0 !important;
  }
}
.contact-form-container .left-content {
  max-width: 433px;
  width: 100%;
}
.contact-form-container .left-content .contact-title-stroke {
  font-weight: 700;
  line-height: 115%;
  color: transparent;
  font-family: Epilogue;
  font-size: 100px;
  background-image: none;
  text-shadow: -1px -1px 0 #ff4a23, 0px -1px 0 #ff4a23, 1px -1px 0 #ff4a23, 1px 0px 0 #ff4a23, 1px 1px 0 #ff4a23, 0px 1px 0 #ff4a23, -1px 1px 0 #ff4a23, -1px 0 0 #ff4a23;
  color: var(--white-color);
  transform-origin: 50% 50% 80px;
}
.contact-form-container .contact-form {
  width: 100%;
  padding: 135px 15px 80px 80px;
}
.contact-form-container .contact-form form {
  max-width: 750px;
}

.comments-box-content .comments-content .comments-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 130%;
  margin-bottom: 40px;
}
.comments-box-content .comments-content .comments-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.comments-box-content .comments-content .comments-info .person-info {
  display: flex;
  gap: 14px;
}
.comments-box-content .comments-content .comments-info .person-info .person-img {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
}
.comments-box-content .comments-content .comments-info .person-desp {
  font-size: 16px;
}
.comments-box-content .comments-content .comments-info .comment-reply-btn {
  display: inline-block;
  border: 0;
  background-color: transparent;
  font-size: 18px;
  text-transform: uppercase;
  color: var(--black-color);
  cursor: pointer;
}
.comments-box-content .comments-content .comments-info .comment-reply-btn:hover {
  color: var(--primary-color);
}

.contact-form-wapper.style-2 .contact-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 130%;
  margin-bottom: 10px;
}
.contact-form-wapper.style-2 .contact-title-desp {
  font-size: 16px;
  line-height: 165%;
}
.contact-form-wapper.style-2 .input-text.style-2 {
  width: 100%;
  font-size: 16px;
  border-radius: 50px;
  padding: 12px 30px;
  color: var(--black-color);
  border: 1px solid var(--border-color);
  background-color: transparent;
}
.contact-form-wapper.style-2 .input-text.style-2:focus {
  border: 1px solid var(--primary-color);
  outline: none;
}
.contact-form-wapper.style-2 .textarea-text.style-2 {
  width: 100%;
  padding: 12px 30px;
  font-size: 16px;
  border-radius: 15px;
  color: var(--black-color);
  border: 1px solid var(--border-color);
  background-color: transparent;
}
.contact-form-wapper.style-2 .textarea-text.style-2:focus {
  border: 1px solid var(--primary-color);
  outline: none;
}
.contact-form-wapper.style-2 .circle-btn.style-1 {
  background-color: var(--white-color);
}
.contact-form-wapper.style-2 .circle-btn.style-1:hover {
  color: var(--white-color);
  background-color: var(--black-color);
}
.contact-form-wapper.style-2 .contact-us-btn {
  font-size: 18px;
  border-radius: 50px;
  padding: 36px 0;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--white-color);
  background-color: var(--black-color);
  width: 100%;
  border: 0;
  justify-content: flex-start;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}
.contact-form-wapper.style-2 .contact-us-btn span {
  position: absolute;
  z-index: 111;
  transition: color 0.3s ease;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border: 1px solid var(--border-color);
  border-radius: 50px;
}
.contact-form-wapper.style-2 .contact-us-btn:before, .contact-form-wapper.style-2 .contact-us-btn:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: var(--white-color);
  transition: transform 0.3s ease;
  z-index: 11;
}
.contact-form-wapper.style-2 .contact-us-btn:before {
  transform: translateY(-100%);
}
.contact-form-wapper.style-2 .contact-us-btn:after {
  transform: translateY(100%);
}
.contact-form-wapper.style-2 .contact-us-btn:hover {
  color: var(--black-color);
}
.contact-form-wapper.style-2 .contact-us-btn:hover:before {
  transform: translateY(-45%);
}
.contact-form-wapper.style-2 .contact-us-btn:hover:after {
  transform: translateY(45%);
}

.contact-cards {
  max-width: 1110px;
  width: 100%;
  display: flex;
  gap: 30px;
  padding: 0 15px;
}
.contact-cards .contact-card {
  background: conic-gradient(from 180deg at 50% 50%, rgba(241, 241, 241, 0.9607843137) 158.4deg, #e0e0e0 180deg);
  padding: 40px 0;
  text-align: center;
  width: 32%;
}
.contact-cards .contact-card .contact-icon {
  margin-bottom: 30px;
}
.contact-cards .contact-card .contact-icon svg path {
  stroke: #ff4a23;
}
.contact-cards .contact-card .contact-title {
  font-size: 28px;
  font-weight: 600;
  line-height: 130%;
}

@media screen and (max-width: 1199px) {
  .contact-form-container {
    flex-wrap: wrap;
  }
  .contact-form-container .left-content {
    max-width: none;
  }
  .contact-form-container .left-content .contact-title-stroke {
    font-size: 80px;
  }
  .contact-form-container .contact-form {
    padding: 50px;
  }
}
@media screen and (max-width: 767px) {
  .contact-cards {
    flex-direction: column;
    align-items: center;
  }
  .contact-cards .contact-card {
    width: 100%;
  }
  .contact-form-container {
    gap: 50px;
  }
  .contact-form-container .left-content .contact-title-stroke {
    font-size: 36px;
  }
  .contact-form-container .contact-form {
    padding: 25px;
  }
}
/*--------------------------------------------------------------
32. Error Page Content
----------------------------------------------------------------*/
.error-page-container {
  margin-top: 50px;
  max-width: 550px;
  width: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  justify-content: center;
}
.error-page-container .number-stroke {
  display: flex;
  align-items: center;
  gap: 10px;
}
.error-page-container .number-stroke .number {
  font-size: clamp(150px, 25vw, 247px);
  font-weight: 700;
  color: transparent;
  line-height: normal;
  background-color: transparent;
  background-image: none;
  text-shadow: -1px -1px 0 var(--black-color), 0px -1px 0 var(--black-color), 1px -1px 0 var(--black-color), 1px 0px 0 var(--black-color), 1px 1px 0 var(--black-color), 0px 1px 0 var(--black-color), -1px 1px 0 var(--black-color), -1px 0 0 var(--black-color);
  color: var(--white-color);
  word-spacing: -32px;
}
.error-page-container .number-stroke .exclamation {
  margin-bottom: 50px;
}
.error-page-container .error-message {
  font-size: 55px;
  line-height: 120%;
  margin-bottom: 10px;
}
.error-page-container .error-description {
  margin-bottom: 50px;
  color: var(--body-color);
}
.error-page-container .home-back-btn {
  border: 1px solid var(--border-color) !important;
  border-radius: 50px !important;
  padding: 15px 25px 15px 28px;
}
.error-page-container .home-back-btn .text-1 {
  margin-top: 5px;
}

.upcomming-soon-container {
  min-height: 100vh;
  padding: 15px;
}
.upcomming-soon-container .upcomming-soon-wapper {
  max-width: 930px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4vw;
}
@media (max-width: 768px) {
  .upcomming-soon-container .upcomming-soon-wapper {
    gap: 50px;
  }
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}
@media (min-width: 992px) {
  .upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content {
    margin-left: 70px;
  }
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content > * {
  width: 22.5%;
}
@media (max-width: 575px) {
  .upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content {
    width: 100%;
  }
  .upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content > * {
    width: 50% !important;
    text-align: center;
  }
  .upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content .funfact.style1 .funfact-card {
    width: 175px !important;
    height: 175px !important;
  }
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content .funfact.style1 .funfact-card {
  width: 246px;
  height: 246px;
  border-radius: 50%;
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .time-counter .funfact-content .funfact.style1 .funfact-card .funfact-number {
  font-size: 70px;
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .upcomming-soon-info {
  max-width: 500px;
  width: 100%;
  text-align: center;
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .upcomming-soon-info .upcomming-soon-title {
  font-size: clamp(50px, 10vw, 100px);
  font-style: italic;
  font-weight: 400;
  line-height: normal;
  font-family: "Instrument Serif";
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .upcomming-soon-info .upcomming-soon-title .highlight {
  color: #ff4a23;
}
.upcomming-soon-container .upcomming-soon-wapper .upcomming-soon-content .upcomming-soon-info .upcomming-soon-desp {
  font-size: 18px;
  line-height: 165%;
  text-align: center;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper {
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .social-content {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  flex-wrap: wrap;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .social-content .social-title {
  max-width: 500px;
  width: 100%;
  font-size: 28px;
  line-height: 130%;
  font-weight: 600;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .social-content .social-title .heghtlight {
  font-family: var(--secondary-font-family);
  color: var(--primary-color);
  font-style: italic;
  font-weight: 400;
  position: relative;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .social-content .social-title .heghtlight.underline::after {
  content: "";
  position: absolute;
  bottom: 3px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 1px;
  background-color: var(--primary-color);
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .email-send-form {
  position: relative;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .email-send-form input {
  width: 100%;
  padding: 20px 30px;
  border-radius: 50px;
  border: 1px solid var(--border-color);
  background-color: var(--white-color);
  transition: all 0.3s ease;
  color: var(--black-color);
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .email-send-form .email-send-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translate(0, -50%);
  background-color: transparent;
  border: 0;
  font-weight: 500;
  line-height: normal;
  text-transform: uppercase;
}
.upcomming-soon-container .upcomming-soon-wapper .social-wapper .email-send-form .email-send-btn button {
  background-color: transparent;
  margin-bottom: 0;
}

@media screen and (max-width: 767px) {
  .error-page-container .number-stroke .exclamation svg {
    width: 100px;
    height: 100px;
  }
}/*# sourceMappingURL=style.css.map */