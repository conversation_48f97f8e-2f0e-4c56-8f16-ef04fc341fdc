/*--------------------------------------------------------------
2. Typography
----------------------------------------------------------------*/

// Google Fonts
@import url("https://fonts.googleapis.com/css2?family=Epilogue:ital,wght@0,100..900;1,100..900&family=Instrument+Serif:ital@0;1&display=swap");
html,
body {
  color: var(--body-color);
  font-family: var(--body-font-family);
  background-color: var(--body-bg-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 165%;
  overflow-x: hidden;
  scroll-behavior: auto;
  padding-right: 0 !important;
  transition: color 0.5s, background-color 0.5s;
}

html.lenis,
html.lenis body {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto !important;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-smooth iframe {
  pointer-events: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  clear: both;
  color: var(--heading-color);
  font-family: var(--heading-font-family);
  padding: 0px;
  margin: 0px;
  line-height: normal;
  font-style: normal;
}

h1 {
  font-size: 100px;
  font-weight: 700;
}

h2 {
  font-size: 55px;
  font-weight: 700;
}

h3 {
  font-size: 45px;
  font-weight: 600;
}

h4 {
  font-size: 35px;
  font-weight: 600;
}

h5 {
  font-size: 28px;
  font-weight: 500;
}

h6 {
  font-size: 22px;
  font-weight: 500;
}

p {
  margin-bottom: 0;
}

ul {
  margin: 0 0 25px 0;
  padding-left: 20px;
  list-style: square outside none;
}

ol {
  padding-left: 20px;
  margin-bottom: 25px;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

blockquote {
  margin: 0 15px;
  font-style: italic;
  font-size: 20px;
  line-height: 1.6em;
  margin: 0;
}

address {
  margin: 0 0 15px;
}

img {
  border: 0;
  max-width: 100%;
  height: auto;
}

a {
  font-size: 18px;
  color: inherit;
  text-decoration: none;
}

button {
  color: inherit;
  transition: all 0.3s ease;
}

a:hover {
  text-decoration: none;
  color: var(--body-color);
}

table {
  width: 100%;
  margin-bottom: 25px;
  th {
    font-weight: 600;
    color: var(--body-color);
  }
  td,
  th {
    border-top: 1px solid $white;
    padding: 11px 10px;
  }
}

dl {
  margin-bottom: 25px;
  dt {
    font-weight: 600;
  }
}

b,
strong {
  font-weight: bold;
}

pre {
  color: var(--body-color);
  border: 1px solid $white;
  font-size: 18px;
  padding: 25px;
  border-radius: 5px;
}

kbd {
  font-size: 100%;
  background-color: var(--body-color);
  border-radius: 5px;
}
