@font-face {
  font-family: "flaticon_cretio";
  src: url("./flaticon_cretio.woff2?27d66299e77734b3747271e5f6c74c41") format("woff2"), url("./flaticon_cretio.woff?27d66299e77734b3747271e5f6c74c41") format("woff"), url("./flaticon_cretio.eot?27d66299e77734b3747271e5f6c74c41#iefix") format("embedded-opentype"), url("./flaticon_cretio.ttf?27d66299e77734b3747271e5f6c74c41") format("truetype"), url("./flaticon_cretio.svg?27d66299e77734b3747271e5f6c74c41#flaticon_cretio") format("svg");
}
i[class^=flaticon-]:before, i[class*=" flaticon-"]:before {
  font-family: flaticon_cretio !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.flaticon-down-arrow:before {
  content: "\f101";
}

.flaticon-down-arrow-1:before {
  content: "\f102";
}

.flaticon-right-arrow:before {
  content: "\f103";
}

.flaticon-facebook-app-symbol:before {
  content: "\f104";
}

.flaticon-twitter:before {
  content: "\f105";
}

.flaticon-instagram:before {
  content: "\f106";
}

.flaticon-arrow:before {
  content: "\f107";
}

.flaticon-check-mark:before {
  content: "\f108";
}

.flaticon-phone-call:before {
  content: "\f109";
}

.flaticon-envelope:before {
  content: "\f10a";
}

.flaticon-right-arrow-1:before {
  content: "\f10b";
}

.flaticon-location:before {
  content: "\f10c";
}

.flaticon-location-1:before {
  content: "\f10d";
}

.flaticon-phone:before {
  content: "\f10e";
}

.flaticon-call:before {
  content: "\f10f";
}

.flaticon-email:before {
  content: "\f110";
}

.flaticon-chevron:before {
  content: "\f111";
}

.flaticon-down-chevron:before {
  content: "\f112";
}

.flaticon-check-mark-1:before {
  content: "\f113";
}

.flaticon-checked:before {
  content: "\f114";
}

.flaticon-facebook:before {
  content: "\f115";
}

.flaticon-facebook-1:before {
  content: "\f116";
}

.flaticon-facebook-logo:before {
  content: "\f117";
}

.flaticon-linkedin:before {
  content: "\f118";
}

.flaticon-instagram-logo:before {
  content: "\f119";
}

.flaticon-video:before {
  content: "\f11a";
}

.flaticon-telephone:before {
  content: "\f11b";
}

.flaticon-phone-call-1:before {
  content: "\f11c";
}

.flaticon-sparkle:before {
  content: "\f11d";
}

.flaticon-compass:before {
  content: "\f11e";
}

.flaticon-star:before {
  content: "\f11f";
}

.flaticon-star-1:before {
  content: "\f120";
}

.flaticon-star-2:before {
  content: "\f121";
}

.flaticon-circles:before {
  content: "\f122";
}

.flaticon-folded:before {
  content: "\f123";
}

.flaticon-saturn:before {
  content: "\f124";
}

.flaticon-overlapping:before {
  content: "\f125";
}

.flaticon-heptagon:before {
  content: "\f126";
}

.flaticon-circles-1:before {
  content: "\f127";
}

.flaticon-twirl:before {
  content: "\f128";
}

.flaticon-asterisk:before {
  content: "\f129";
}

.flaticon-star-3:before {
  content: "\f12a";
}

.flaticon-circle:before {
  content: "\f12b";
}

.flaticon-zigzag-lines-in-side-view-position:before {
  content: "\f12c";
}

.flaticon-sparkler:before {
  content: "\f12d";
}

.flaticon-right-arrow-2:before {
  content: "\f12e";
}

.flaticon-arrow-up:before {
  content: "\f12f";
}

.flaticon-top-right:before {
  content: "\f130";
}

.flaticon-left-up:before {
  content: "\f131";
}

.flaticon-down-right-arrow:before {
  content: "\f132";
}

.flaticon-left-down-arrow:before {
  content: "\f133";
}

.flaticon-right-arrow-3:before {
  content: "\f134";
}

.flaticon-down-right-arrow-1:before {
  content: "\f135";
}

.flaticon-down-arrow-2:before {
  content: "\f136";
}

.flaticon-left-arrow:before {
  content: "\f137";
}

.flaticon-top-left:before {
  content: "\f138";
}

.flaticon-bottom-left:before {
  content: "\f139";
}

.flaticon-up-arrow:before {
  content: "\f13a";
}

.flaticon-right-arrow-4:before {
  content: "\f13b";
}

.flaticon-up-arrow-1:before {
  content: "\f13c";
}

.flaticon-left-arrow-1:before {
  content: "\f13d";
}

.flaticon-down-arrow-3:before {
  content: "\f13e";
}

.flaticon-up-arrow-2:before {
  content: "\f13f";
}

.flaticon-down-arrow-4:before {
  content: "\f140";
}

.flaticon-up-right-arrow:before {
  content: "\f141";
}

.flaticon-left-arrow-2:before {
  content: "\f142";
}

.flaticon-sun:before {
  content: "\f143";
}

.flaticon-night-mode:before {
  content: "\f144";
}

.flaticon-close:before {
  content: "\f145";
}

.flaticon-close-button:before {
  content: "\f146";
}

.flaticon-close-1:before {
  content: "\f147";
}

.flaticon-close-button-1:before {
  content: "\f148";
}

.flaticon-dots-menu:before {
  content: "\f149";
}

.flaticon-menu:before {
  content: "\f14a";
}

.flaticon-grid:before {
  content: "\f14b";
}/*# sourceMappingURL=flaticon_cretio.css.map */