/* Global Styles */
:root {
    --primary-color: #FF4A23;
    --text-color: #ffffff;
    --dark-bg: #000000;
    --heading-font: 'SpaceGrotesk', sans-serif;
    --body-font: 'Inter', sans-serif;
}

/* Reset and Base Styles */
body {
    margin: 0;
    padding: 0;
    font-family: var(--body-font);
    background-color: var(--dark-bg);
    color: var(--text-color);
    line-height: 1.6;
}

/* Main Hero Section */
.hero-section-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0;
}

.hero-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.title-wrapper {
    position: relative;
}

.main-title {
    font-family: var(--heading-font);
    font-size: 92px;
    line-height: 1.1;
    font-weight: 700;
    margin: 0;
    padding: 0;
}

.cta-links {
    margin-top: 30px;
}

.link-style {
    color: var(--text-color);
    text-decoration: none;
    font-size: 18px;
    margin-right: 30px;
    position: relative;
}

.link-style:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.link-style:hover:after {
    width: 100%;
}

/* Let's Build Section */
.lets-build-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-color: var(--dark-bg);
    position: relative;
    overflow: hidden;
}

.build-content {
    position: relative;
    z-index: 2;
}

.build-title {
    font-family: var(--heading-font);
    font-size: 82px;
    margin: 0;
    line-height: 1;
}

.tagline {
    margin: 30px 0;
    font-size: 72px;
    line-height: 1.2;
}

.creative-text {
    color: var(--primary-color);
    font-style: italic;
}

.timeless {
    color: var(--text-color);
}

.contact-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 40px;
    margin-top: 60px;
}

.contact-circle {
    width: 150px;
    height: 150px;
}

.circle-button {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    transition: transform 0.3s ease;
}

.circle-button:hover {
    transform: scale(1.05);
}

.contact-info {
    padding-top: 20px;
}

.hello-text {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

.email {
    color: var(--text-color);
    text-decoration: none;
    font-size: 20px;
    display: block;
    margin-top: 5px;
}

/* Logo size adjustment */
.brand-logo {
    max-width: 600px; /* Increased from 400px */
    margin: 0 auto;
    padding: 20px;
}

.brand-logo .offcanvas-logo-content {
    text-align: center;
}

.brand-logo img {
    width: 100%;
    height: auto;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-title {
        font-size: 72px;
    }
    
    .tagline {
        font-size: 60px;
    }
}

@media (max-width: 768px) {
    .main-title {
        font-size: 48px;
    }
    
    .tagline {
        font-size: 42px;
    }
    
    .contact-wrapper {
        flex-direction: column;
        align-items: center;
    }
}

body.dark {
    background-color: var(--dark-bg);
    color: var(--text-color);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 0;
}

.hero-title {
    font-size: 72px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 40px;
}

.hero-actions {
    display: flex;
    gap: 20px;
}

/* Let's Build Section */
.lets-build-section {
    padding: 100px 0;
    background: url('../img/bg/footer-bg.png') no-repeat center;
    background-size: cover;
}

.creative-text {
    font-size: 64px;
    margin: 30px 0;
}

.creative-text .highlight {
    color: var(--primary-color);
    font-style: italic;
}

/* Services Section */
.services-section {
    padding: 100px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.service-item {
    padding: 30px;
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.service-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
}

/* Navigation and Footer Spacing */
.navigation-section {
    padding: 50px 0;
}

.footer-nav {
    display: flex;
    justify-content: center;
    gap: 30px;
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Company Info Section */
.company-info {
    padding: 80px 0;
    background: rgba(255,255,255,0.02);
}

.info-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.company-desc {
    font-size: 24px;
    line-height: 1.6;
    margin: 30px 0;
}

.highlight {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 48px;
    }
    
    .creative-text {
        font-size: 42px;
    }
    
    .company-desc {
        font-size: 18px;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
