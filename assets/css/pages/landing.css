/* Main Title Section */
.main-title-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 20px;
}

.main-title {
    font-size: 120px;
    line-height: 1.1;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 40px;
}

.actions {
    display: flex;
    gap: 40px;
}

.actions a {
    color: #ffffff;
    text-decoration: none;
    font-size: 18px;
    position: relative;
}

.actions a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #FF4A23;
    transition: width 0.3s ease;
}

.actions a:hover::after {
    width: 100%;
}

/* Let's Build Section */
.lets-build-content {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 20px;
}

.section-title {
    font-size: 100px;
    line-height: 1;
    color: #ffffff;
    margin-bottom: 20px;
}

.tagline {
    font-size: 90px;
    line-height: 1.2;
    margin-bottom: 60px;
}

.tagline .highlight {
    color: #FF4A23;
    font-style: italic;
}

.contact-section {
    display: flex;
    align-items: flex-start;
    gap: 40px;
}

.contact-circle {
    width: 180px;
    height: 180px;
}

.circle-btn {
    width: 100%;
    height: 100%;
    background: #FF4A23;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 18px;
    line-height: 1.4;
    transition: transform 0.3s ease;
}

.circle-btn:hover {
    transform: scale(1.05);
}

.contact-info {
    padding-top: 20px;
}

.hello-text {
    font-size: 18px;
    color: #ffffff;
    opacity: 0.8;
    margin: 0;
}

.contact-email {
    color: #ffffff;
    text-decoration: none;
    font-size: 24px;
    display: block;
    margin-top: 5px;
}

/* Company Info Section */
.company-info {
    padding: 100px 20px;
}

.logo {
    margin-bottom: 40px;
}

.description {
    font-size: 24px;
    line-height: 1.6;
    color: #ffffff;
    max-width: 800px;
}

.description .highlight {
    color: #FF4A23;
}

.contact-details {
    margin-top: 40px;
}

.contact-details a {
    color: #ffffff;
    text-decoration: none;
    font-size: 24px;
    display: block;
    margin-bottom: 10px;
}

.address {
    color: #ffffff;
    opacity: 0.8;
    margin: 0;
}
