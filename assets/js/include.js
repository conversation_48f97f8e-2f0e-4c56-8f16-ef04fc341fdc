(function () {
  "use strict";

  // Function to include HTML content
  const includeHTML = (elementId, filePath) => {
    const element = document.getElementById(elementId);
    if (element) {
      fetch(filePath)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.text();
        })
        .then((data) => {
          element.innerHTML = data;
        })
        .catch((error) => {
          console.error("Error fetching HTML:", error);
        });
    }
  };

  // No includes needed for coming-soon.html as content is already in the page
  document.addEventListener("DOMContentLoaded", function () {
    // coming-soon.html has all content embedded
  });
})();