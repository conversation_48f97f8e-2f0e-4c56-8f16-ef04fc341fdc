// Countdown Timer Module
const CountdownTimer = {
    targetDate: new Date('2025-08-14T00:00:00').getTime(),
    elements: {},
    interval: null,

    init: function() {
        // Initialize only when DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    },

    setup: function() {
        // Cache DOM elements
        this.elements = {
            months: document.querySelector('#months span'),
            days: document.querySelector('#days span'),
            hours: document.querySelector('#hours span'),
            minutes: document.querySelector('#minutes span'),
            seconds: document.querySelector('#seconds span'),
            counter: document.querySelector('.time-counter')
        };

        // Verify all elements exist
        if (this.elementsExist()) {
            this.start();
        }
    },

    elementsExist: function() {
        return Object.values(this.elements).every(element => element !== null);
    },

    calculate: function() {
        const now = new Date().getTime();
        const distance = this.targetDate - now;

        if (distance < 0) {
            this.stop();
            this.elements.counter.innerHTML = "<h2>Website Launched!</h2>";
            return null;
        }

        // Calculate all time units
        return {
            months: Math.floor(distance / (1000 * 60 * 60 * 24 * 30)),
            days: Math.floor((distance % (1000 * 60 * 60 * 24 * 30)) / (1000 * 60 * 60 * 24)),
            hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
            seconds: Math.floor((distance % (1000 * 60)) / 1000)
        };
    },

    pad: function(num) {
        return num.toString().padStart(2, '0');
    },

    update: function() {
        const timeLeft = this.calculate();
        
        if (timeLeft === null) return;

        // Update the DOM
        this.elements.months.textContent = this.pad(timeLeft.months);
        this.elements.days.textContent = this.pad(timeLeft.days);
        this.elements.hours.textContent = this.pad(timeLeft.hours);
        this.elements.minutes.textContent = this.pad(timeLeft.minutes);
        this.elements.seconds.textContent = this.pad(timeLeft.seconds);
    },

    start: function() {
        // Initial update
        this.update();
        
        // Clear any existing interval
        if (this.interval) {
            clearInterval(this.interval);
        }
        
        // Start the countdown
        this.interval = setInterval(() => this.update(), 1000);
    },

    stop: function() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }
};

// Start the countdown
CountdownTimer.init();
