<!-- Theme Switcher -->
<div class="mode-toggle">
    <div class="setting-mode">
        <button id="open">
            <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
            <i class="flaticon-close-button-1"></i>
        </button>
    </div>
    <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
            <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
            <i class="flaticon-night-mode"></i>
        </button>
    </div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Theme switcher functionality
    const body = document.body;
    const modeButtons = document.querySelectorAll('.js-mode-type button');
    const openBtn = document.getElementById('open');
    const clecelBtn = document.getElementById('clecel');
    const modeToggle = document.querySelector('.mode-toggle');

    // Check saved theme
    const savedTheme = localStorage.getItem('theme') || 'dark';
    body.classList.toggle('dark', savedTheme === 'dark');
    body.classList.toggle('light', savedTheme === 'light');

    // Update active button state
    modeButtons.forEach(button => {
        button.classList.toggle('active', button.dataset.mode === savedTheme);
    });

    // Theme switch buttons
    modeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const mode = button.dataset.mode;
            localStorage.setItem('theme', mode);
            
            body.classList.remove('dark', 'light');
            body.classList.add(mode);
            
            modeButtons.forEach(btn => {
                btn.classList.toggle('active', btn === button);
            });
        });
    });

    // Toggle theme switcher visibility
    openBtn.addEventListener('click', () => {
        modeToggle.classList.add('active');
    });

    clecelBtn.addEventListener('click', () => {
        modeToggle.classList.remove('active');
    });
});
</script>
<!-- End Theme Switcher -->
