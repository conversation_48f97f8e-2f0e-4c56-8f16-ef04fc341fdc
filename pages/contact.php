<?php
$page_title = "Contact Us";
// Common header with meta tags and CSS
include_once('../includes/components/preloader.php');
include_once('../includes/components/header.php');
?>

<!-- Start Breadcrumb -->
<div class="ak-height-150 ak-height-lg-120"></div>
<div class="breadcrumb-area style-2">
    <div class="container">
        <div class="breadcrumb-wapper style-2">
            <div class="breadcrumb-title-box">
                <h1 class="breadcrumb-title ak-w-70">
                    <span class="highlight-text">Get In Touch</span> With our Digital
                    <span class="highlight-text">Professional Team</span>
                </h1>
                <div class="breadcrumb-caption">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="223" height="12" viewBox="0 0 223 12" fill="none">
                            <path d="M1.33789 1.18359H221.034L209.173 10.9822" stroke="#FF4A23" stroke-linecap="round"></path>
                        </svg>
                    </span>
                    <span><a href="/">Home</a> / Contact Us </span>
                </div>
            </div>
        </div>
    </div>
    <div class="breadcrumb-stroke text-normal">CONTACT</div>
</div>
<!-- End Breadcrumb -->

<!-- Start Contact Form-->
<div class="ak-height-150 ak-height-lg-80"></div>
<section>
    <div class="ak-center">
        <div class="theme-border-wrap contact-form-border hover-animation">
            <div class="b-top-left">
                <div class="horizontal"></div>
                <div class="verticle"></div>
            </div>
            <div class="b-top-right d-flex">
                <div class="horizontal"></div>
                <div class="verticle"></div>
            </div>
            <div class="b-bottom-right d-flex flex-end">
                <div class="horizontal flex-end align-self-end"></div>
                <div class="verticle"></div>
            </div>
            <div class="b-bottom-left">
                <div class="verticle"></div>
                <div class="horizontal"></div>
            </div>

            <div class="container">
                <div class="ak-center">
                    <div class="contact-form-box">
                        <div class="contact-form-wapper style-2">
                            <h5 class="contact-title text-center">Drop a Message</h5>
                            <div class="ak-height-30 ak-height-lg-30"></div>
                            <form id="contactForm" method="POST" action="process_contact.php">
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <input type="text" class="input-text style-2" placeholder="First name" aria-label="First name" name="firstName" required />
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="input-text style-2" placeholder="Last name" name="lastName" aria-label="Last name" required />
                                    </div>
                                    <div class="col-md-6">
                                        <input type="email" class="input-text style-2" placeholder="Email" aria-label="Email" name="email" required />
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="input-text style-2" placeholder="Phone" name="phone" aria-label="Phone" required />
                                    </div>
                                    <div class="col-md-12">
                                        <input type="text" class="input-text style-2" placeholder="Subject" name="subject" aria-label="Subject" required />
                                    </div>
                                    <div class="col-md-12">
                                        <textarea name="message" class="textarea-text style-2" rows="5" placeholder="Your Message..."></textarea>
                                    </div>
                                </div>
                                <div class="ak-height-40 ak-height-lg-30"></div>
                                <div class="d-inline">
                                    <button type="submit" class="contact-us-btn">
                                        <span class="ak-center">Send Message</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ak-height-75 ak-height-lg-40"></div>
        </div>
    </div>
</section>
<!-- End Contact Form-->

<!-- Start Contact Cards -->
<div class="ak-height-150 ak-height-lg-80"></div>
<div class="">
    <div class="ak-center">
        <div class="contact-cards">
            <div class="contact-card">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="51" viewBox="0 0 50 51" fill="none">
                        <g clip-path="url(#clip0_2385_90)">
                            <path d="M12.7442 25.9029L12.735 25.8885L12.7253 25.8745C10.8478 23.1543 9.88672 20.2845 9.88672 17.0669C9.88672 8.59315 16.7565 1.95361 25 1.95361C33.3326 1.95361 40.1133 8.73432 40.1133 17.0669C40.1133 20.21 39.1534 23.2283 37.3328 25.802L37.3197 25.8205L37.3075 25.8396L25.3914 44.4162L25.3906 44.4174C25.2089 44.7016 24.791 44.7015 24.6093 44.4174L24.6088 44.4166L12.7442 25.9029ZM11.9022 26.4425L23.7669 44.9562L38.1492 26.3795C40.0891 23.6372 41.1133 20.4172 41.1133 17.0669C41.1133 8.18203 33.8849 0.953613 25 0.953613C16.2144 0.953613 8.88672 8.03076 8.88672 17.0669C8.88672 20.5046 9.91973 23.57 11.9022 26.4425ZM16.6758 17.0669C16.6758 21.6576 20.4093 25.3911 25 25.3911C29.5907 25.3911 33.3242 21.6576 33.3242 17.0669C33.3242 12.4762 29.5907 8.74268 25 8.74268C20.4093 8.74268 16.6758 12.4762 16.6758 17.0669Z" stroke="#FF4A23" stroke-width="2"/>
                            <path d="M29.9172 46.6871L29.9179 46.686L36.91 35.7544C39.7677 36.4741 42.0239 37.4351 43.5744 38.5343C45.2513 39.7232 45.9727 40.9699 45.9727 42.1646C45.9727 43.2238 45.4578 44.2345 44.3729 45.1991C43.2772 46.1732 41.6604 47.0396 39.6449 47.7581C35.6201 49.1929 30.247 49.9536 25 49.9536C19.753 49.9536 14.3799 49.1929 10.3551 47.7581C8.33959 47.0396 6.72284 46.1732 5.62714 45.1991C4.54224 44.2345 4.02734 43.2238 4.02734 42.1646C4.02734 40.9708 4.74773 39.7249 6.4222 38.5367C7.97035 37.4382 10.2234 36.4775 13.0773 35.7576L20.081 46.6895L20.0812 46.6897C22.3741 50.2666 27.6138 50.2783 29.9172 46.6871Z" stroke="#FF4A23" stroke-width="2"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_2385_90">
                                <rect width="50" height="50" fill="white" transform="translate(0 0.953613)"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
                <h3 class="contact-title">Address</h3>
                <p class="contact-text">901 N Pitt Str., Suite 170<br/>Alexandria, USA</p>
            </div>
            <div class="contact-card">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="51" viewBox="0 0 50 51" fill="none">
                        <path d="M33.4217 34.7399L33.421 34.7406C32.1822 35.969 30.9504 37.1904 29.7402 38.4316C29.5156 38.6631 29.1889 38.9329 28.7369 39.0233C28.2616 39.1184 27.8442 38.9786 27.5196 38.7968C27.2242 38.6359 26.9246 38.4886 26.6039 38.3309C26.5163 38.2878 26.4271 38.244 26.336 38.1988C25.9218 37.9937 25.4797 37.7679 25.0514 37.5017L25.0469 37.4989C21.3423 35.1687 18.2568 32.1838 15.5443 28.8567L15.5438 28.8561C14.1871 27.1896 12.9499 25.3695 12.0784 23.2955" stroke="white" stroke-width="2"/>
                        <path d="M29.9172 46.6871L29.9179 46.686L36.91 35.7544C39.7677 36.4741 42.0239 37.4351 43.5744 38.5343C45.2513 39.7232 45.9727 40.9699 45.9727 42.1646C45.9727 43.2238 45.4578 44.2345 44.3729 45.1991C43.2772 46.1732 41.6604 47.0396 39.6449 47.7581C35.6201 49.1929 30.247 49.9536 25 49.9536C19.753 49.9536 14.3799 49.1929 10.3551 47.7581C8.33959 47.0396 6.72284 46.1732 5.62714 45.1991C4.54224 44.2345 4.02734 43.2238 4.02734 42.1646C4.02734 40.9708 4.74773 39.7249 6.4222 38.5367C7.97035 37.4382 10.2234 36.4775 13.0773 35.7576L20.081 46.6895L20.0812 46.6897C22.3741 50.2666 27.6138 50.2783 29.9172 46.6871Z" stroke="white" stroke-width="2"/>
                    </svg>
                </div>
                <h3 class="contact-title">Phone</h3>
                <p class="contact-text">(*************<br/>(*************</p>
            </div>
            <div class="contact-card">
                <div class="contact-icon email">
                    <svg xmlns="http://www.w3.org/2000/svg" width="70" height="41" viewBox="0 0 70 41" fill="none">
                        <path d="M63.4598 0.953552H15.7674C12.3828 0.953552 9.61358 3.72278 9.61358 7.1074V9.41509C9.61358 10.2612 10.3059 10.9536 11.152 10.9536C11.9982 10.9536 12.6905 10.2612 12.6905 9.41509V7.1074C12.6905 6.79971 12.7674 6.49201 12.8444 6.18432L30.0751 20.9535L12.8444 35.7228C12.7674 35.4151 12.6905 35.1074 12.6905 34.7997V32.492C12.6905 31.6459 11.9982 30.9535 11.152 30.9535C10.3059 30.9535 9.61358 31.6459 9.61358 32.492V34.7997C9.61358 38.1843 12.3828 40.9535 15.7674 40.9535H63.4598C66.8444 40.9535 69.6136 38.1843 69.6136 34.7997V7.1074C69.6136 3.72278 66.8444 0.953552 63.4598 0.953552ZM15.152 4.1074C15.3059 4.03047 15.5367 4.03047 15.7674 4.03047H63.4598C63.6905 4.03047 63.9213 4.03047 64.0751 4.1074L40.6136 24.1843C39.9982 24.6459 39.229 24.6459 38.6136 24.1843L15.152 4.1074ZM63.4598 37.8766H15.7674C15.5367 37.8766 15.3059 37.8766 15.152 37.7997L32.4597 22.9535L36.6136 26.5689C37.4597 27.3382 38.5367 27.7228 39.6136 27.7228C40.6905 27.7228 41.7674 27.3382 42.6136 26.5689L46.7674 22.9535L64.0751 37.7997C63.9213 37.8766 63.6905 37.8766 63.4598 37.8766ZM66.5367 34.7997C66.5367 35.1074 66.4598 35.4151 66.3828 35.7228L49.1521 20.9535L66.3828 6.18432C66.4598 6.49201 66.5367 6.79971 66.5367 7.1074V34.7997Z" fill="#FF4A23"/>
                    </svg>
                </div>
                <h3 class="contact-title">Email</h3>
                <p class="contact-text"><EMAIL><br/><EMAIL></p>
            </div>
        </div>
    </div>
</div>
<!-- End Contact Cards -->

<!-- Start Google Map -->
<div class="ak-height-150 ak-height-lg-100"></div>
<div class="ak-google-map ak-bg">
    <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d96652.27317354927!2d-74.33557928194516!3d40.79756494697628!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c3a82f1352d0dd%3A0x81d4f72c4435aab5!2sTroy+Meadows+Wetlands!5e0!3m2!1sen!2sbd!4v1563075599994!5m2!1sen!2sbd" allowfullscreen loading="lazy"></iframe>
</div>
<!-- End Google Map -->

<?php
include_once('../includes/components/theme-switcher.php');
include_once('../includes/components/footer.php');
?>

<!-- Scripts -->
<script src="../assets/js/plugins/jquery-3.7.1.min.js"></script>
<script src="../assets/js/plugins/bootstrap.bundle.min.js"></script>
<script src="../assets/js/plugins/swiper.min.js"></script>
<script src="../assets/js/plugins/gsap.js"></script>
<script src="../assets/js/plugins/lenis.min.js"></script>
<script src="../assets/js/plugins/splittext.js"></script>
<script src="../assets/js/plugins/scrolltigger.js"></script>
<script src="../assets/js/plugins/scrolltoplugins.js"></script>
<script src="../assets/js/main.js"></script>

<!-- Form handling script -->
<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    fetch('process_contact.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if(data.success) {
            alert('Thank you for your message. We will contact you soon!');
            this.reset();
        } else {
            alert('There was an error sending your message. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('There was an error sending your message. Please try again.');
    });
});
</script>
</body>
</html>
