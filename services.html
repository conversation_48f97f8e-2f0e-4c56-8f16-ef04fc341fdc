<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
<!-- Master Head Template for All Pages (Except index.html) -->
<!-- This template provides consistent SEO, branding, and styling across all pages -->

<!-- Meta Tags -->
<meta charset="utf-8" />
<meta http-equiv="x-ua-compatible" content="ie=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="author" content="Artix Digital Agency" />
<meta name="description" content="Explore Artix's comprehensive digital services: web development, mobile apps, branding, UI/UX design, and digital marketing solutions." />
<meta name="keywords" content="digital services, web development, mobile apps, branding, UI UX design, digital marketing" />
<meta name="robots" content="index, follow" />

<!-- Open Graph Meta Tags for Social Media -->
<meta property="og:title" content="Our Services - Digital Solutions & Creative Design - Artix Digital Agency" />
<meta property="og:description" content="Explore Artix's comprehensive digital services: web development, mobile apps, branding, UI/UX design, and digital marketing solutions." />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://artix.co.in/services.html" />
<meta property="og:image" content="assets/img/logo/artix-og-image.png" />
<meta property="og:site_name" content="Artix Digital Agency" />

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="Our Services - Digital Solutions & Creative Design - Artix Digital Agency" />
<meta name="twitter:description" content="Explore Artix's comprehensive digital services: web development, mobile apps, branding, UI/UX design, and digital marketing solutions." />
<meta name="twitter:image" content="assets/img/logo/artix-twitter-image.png" />
<meta name="twitter:site" content="@ArtixDigital" />

<!-- Favicon and Touch Icons -->
<link rel="icon" href="assets/img/logo/favicon.png" />
<link rel="apple-touch-icon" href="assets/img/logo/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="assets/img/logo/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="assets/img/logo/favicon-16x16.png" />

<!-- Site Title -->
<title>Our Services - Digital Solutions & Creative Design | Artix - Branding | Creative | Designs</title>

<!-- Preload Critical Resources -->
<link rel="preload" href="assets/css/style.css" as="style" />
<link rel="preload" href="assets/js/main.js" as="script" />
<link rel="preload" href="assets/fonts/flaticon_cretio.css" as="style" />

<!-- DNS Prefetch for External Resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />

<!-- Stylesheets -->
<link rel="stylesheet" type="text/css" href="assets/fonts/flaticon_cretio.css" />
<link rel="stylesheet" href="assets/css/plugins/swiper.min.css" />
<link rel="stylesheet" href="assets/css/plugins/bootstrap.min.css" />
<link rel="stylesheet" href="assets/css/style.css" />
<link rel="stylesheet" href="assets/css/custom.css" />

<!-- Enhanced Global Styles -->
<style>
  /* Global Enhanced Preloader Styles */
  .preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--body-bg-color);
    z-index: 99999999;
    overflow: hidden;
    transform: translateY(0);
    transition: opacity 0.5s ease-in-out;
  }
  
  .preloader .txt-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
    color: var(--heading-color);
  }
  
  .preloader-text-svg {
    width: 300px;
    height: auto;
    max-width: 80vw;
  }
  
  .svg-text {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 2;
    font-size: 120px;
    font-family: var(--heading-font-family);
    font-weight: 700;
    opacity: 0;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawText 3s ease-in-out forwards;
  }
  
  .loading-percent {
    margin-top: 2rem;
    font-size: 18px;
    font-weight: 500;
    color: var(--body-color);
    opacity: 0;
    animation: fadeInPercent 0.5s ease-in-out 1s forwards;
  }
  
  @keyframes drawText {
    0% {
      stroke-dashoffset: 1000;
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      stroke-dashoffset: 0;
      opacity: 1;
      fill: var(--primary-color);
    }
  }
  
  @keyframes fadeInPercent {
    to {
      opacity: 1;
    }
  }
  
  /* Global Page Enhancement Styles */
  .page-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .page-content.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Reading Progress Bar */
  .reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #FF6B47);
    z-index: 9999;
    transition: width 0.3s ease;
  }
  
  /* Global Image Loading Animation */
  .img-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-in-out;
  }
  
  .img-animate.loaded {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Global Smooth Scroll */
  html {
    scroll-behavior: smooth;
  }
  
  /* Global Responsive Enhancements */
  @media (max-width: 768px) {
    .preloader-text-svg {
      width: 250px;
    }
    
    .svg-text {
      font-size: 80px;
    }
    
    .loading-percent {
      font-size: 16px;
    }
  }
  
  @media (max-width: 480px) {
    .preloader-text-svg {
      width: 200px;
    }
    
    .svg-text {
      font-size: 60px;
    }
  }
  
  /* Global Performance Optimizations */
  * {
    box-sizing: border-box;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* Global Accessibility Improvements */
  @media (prefers-reduced-motion: reduce) {
    .svg-text {
      animation: none;
      opacity: 1;
      stroke-dashoffset: 0;
      fill: var(--primary-color);
    }
    
    .loading-percent {
      animation: none;
      opacity: 1;
    }
    
    .page-content,
    .img-animate {
      transition: none;
    }
  }
</style>

<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Artix Digital Agency",
  "url": "https://artix.co.in",
  "logo": "https://artix.co.in/assets/img/logo/dark-logo.png",
  "description": "Professional digital agency offering branding, creative designs, web development, and mobile app development services.",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "IN",
    "addressLocality": "India"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+91 81065 34629",
    "contactType": "customer service",
    "email": "<EMAIL>"
  },
  "sameAs": [
    "https://facebook.com/artixdigital",
    "https://twitter.com/artixdigital",
    "https://linkedin.com/company/artixdigital"
  ]
}
</script>

</head>

<body class="dark">
    <!-- Enhanced Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#FF4A23" />
                <stop offset="50%" stop-color="#FF6B47" />
                <stop offset="100%" stop-color="#FF4A23" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle">Artix</text>
          </svg>
        </div>
        <div class="loading-percent" id="loadingPercent">0%</div>
      </div>
    </div>
    <!-- End Enhanced Preloader -->
    
    <!-- Dynamic Header Placeholder -->
    <div id="header"></div>
    
    <!-- Page Content -->
    <main class="page-content">
<!-- Start Breadcrumb -->
    <div class="ak-height-150 ak-height-lg-120"></div>
    <div class="breadcrumb-area">
      <div class="container">
        <div class="breadcrumb-wapper style-2">
          <div class="breadcrumb-title-box">
            <h1
              class="breadcrumb-title text-animation"
              data-direction="textRotate"
              data-split-text="lines"
              data-duration="1.5"
            >
              Our <span class="highlight-text">Exceptional</span> Digital
              Transformation
              <span class="highlight-text">Services</span>
            </h1>
            <div class="breadcrumb-caption">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="223"
                  height="12"
                  viewBox="0 0 223 12"
                  fill="none"
                >
                  <path
                    d="M1.33789 1.18359H221.034L209.173 10.9822"
                    stroke="#FF4A23"
                    stroke-linecap="round"
                  ></path>
                </svg>
              </span>
              <span><a href="/">Home</a> / Services </span>
            </div>
          </div>
          <div>
            <div class="breadcrumb-cricle">
              <div class="cricle-animated-text">
                <div class="rounded-text rotating">
                  <svg viewBox="0 0 200 200">
                    <path
                      id="textPath"
                      d="M 85,0 A 85,85 0 0 1 -85,0 A 85,85 0 0 1 85,0"
                      transform="translate(100,100)"
                      fill="none"
                      stroke-width="0"
                    ></path>
                    <g font-size="22.1px">
                      <text text-anchor="start">
                        <textPath
                          class="coloring"
                          xlink:href="#textPath"
                          startOffset="0%"
                        >
                          DIGITAL PRESENCE CREATIVITY & INNOVATION I N &nbsp;
                        </textPath>
                      </text>
                    </g>
                  </svg>
                </div>
                <div class="cricle-ceneter-text"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- End Breadcrumb -->

    <!-- Start Scroll text -->
    <div class="ak-height-150 ak-height-lg-50"></div>
    <div class="background-gradient">
      <div class="text-container">
        <span class="text-gradient">
          Digital Design / Product Design / Brand Design / Digital Design /
          Product
        </span>
        <span class="text-gradient">
          Digital Design / Product Design / Brand Design / Digital Desig /
          Product
        </span>
      </div>
    </div>
    <!-- End Scroll text -->

    <!-- Start Services -->
    <div class="ak-height-100 ak-height-lg-30"></div>
    <section>
      <div class="container">
        <div class="service-content">
          <div class="service-card fade-animation" data-direction="bottom">
            <img
              class="service-hover-img"
              src="assets/img/services/services-hover-1.png"
              alt="..."
            />
            <div class="service-card-item style-1">
              <div class="service-left-info">
                <h4 class="service-title">UI/UX Design</h4>
                <ul class="service-lists">
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Brand Research </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Competitor Analysis </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Design Structure </span>
                  </li>
                </ul>
              </div>
              <div class="service-left-right">
                <p class="service-desp">
                  Lorem Ipsum is simply dummy text of the printing and
                  typesetting industry. Lorem Ipsum has been industry and
                  typesetting.
                </p>
                <div class="service-btn-content">
                  <a href="service-details.html" class="more-btn">
                    <span class="morebtn-text"> Learn More </span>
                    <span class="primary-icon-anim">
                      <i class="flaticon-up-right-arrow"></i>
                      <i class="flaticon-up-right-arrow"></i>
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div class="service-stroke-number ak-stroke-number">01</div>
          </div>
          <div class="service-card fade-animation" data-direction="bottom">
            <img
              class="service-hover-img"
              src="assets/img/services/services-hover-2.png"
              alt="..."
            />
            <div class="service-card-item style-1">
              <div class="service-left-info">
                <h4 class="service-title">Web Design</h4>
                <ul class="service-lists">
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Brand Research </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span>Design Analysis</span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span>Design Structure</span>
                  </li>
                </ul>
              </div>
              <div class="service-left-right">
                <p class="service-desp">
                  Lorem Ipsum is simply dummy text of the printing and
                  typesetting industry. Lorem Ipsum has been industry and
                  typesetting.
                </p>
                <div class="service-btn-content">
                  <a href="service-details.html" class="more-btn">
                    <span class="morebtn-text"> Learn More </span>
                    <span class="primary-icon-anim">
                      <i class="flaticon-up-right-arrow"></i>
                      <i class="flaticon-up-right-arrow"></i>
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div class="service-stroke-number ak-stroke-number">02</div>
          </div>
          <div class="service-card fade-animation" data-direction="bottom">
            <img
              class="service-hover-img"
              src="assets/img/services/services-hover-3.png"
              alt="..."
            />
            <div class="service-card-item style-1">
              <div class="service-left-info">
                <h4 class="service-title">Web Development</h4>
                <ul class="service-lists">
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Brand Research </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Competitor Analysis</span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span>Modern Code Structure</span>
                  </li>
                </ul>
              </div>
              <div class="service-left-right">
                <p class="service-desp">
                  Lorem Ipsum is simply dummy text of the printing and
                  typesetting industry. Lorem Ipsum has been industry and
                  typesetting.
                </p>
                <div class="service-btn-content">
                  <a href="service-details.html" class="more-btn">
                    <span class="morebtn-text"> Learn More </span>
                    <span class="primary-icon-anim">
                      <i class="flaticon-up-right-arrow"></i>
                      <i class="flaticon-up-right-arrow"></i>
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div class="service-stroke-number ak-stroke-number">03</div>
          </div>
          <div class="service-card fade-animation" data-direction="bottom">
            <img
              class="service-hover-img"
              src="assets/img/services/services-hover-4.png"
              alt="..."
            />
            <div class="service-card-item style-1">
              <div class="service-left-info">
                <h4 class="service-title">App Development</h4>
                <ul class="service-lists">
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Brand Research </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span> Competitor Analysis </span>
                  </li>
                  <li class="service-list">
                    <span>
                      <i class="flaticon-star-2"></i>
                    </span>
                    <span>Modern Code Structure</span>
                  </li>
                </ul>
              </div>
              <div class="service-left-right">
                <p class="service-desp">
                  Lorem Ipsum is simply dummy text of the printing and
                  typesetting industry. Lorem Ipsum has been industry and
                  typesetting.
                </p>
                <div class="service-btn-content">
                  <a href="service-details.html" class="more-btn">
                    <span class="morebtn-text"> Learn More </span>
                    <span class="primary-icon-anim">
                      <i class="flaticon-up-right-arrow"></i>
                      <i class="flaticon-up-right-arrow"></i>
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div class="service-stroke-number ak-stroke-number">04</div>
          </div>
        </div>
      </div>
    </section>
    <!-- End Services -->

    <!-- Start Core Feature -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <section class="core-features-area ak-gray-bg">
      <div class="ak-height-150 ak-height-lg-80"></div>
      <div class="container">
        <div class="ak-section-heading ak-style-1">
          <div class="ak-section-left">
            <h2 class="ak-section-title text-animation">
              Our <span class="highlight">Exceptional</span> Digital
              Transformation <span class="highlight">Services</span>
            </h2>
          </div>
          <div class="ak-section-right">
            <p class="ak-section-desp">
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. has been industry and typesetting of the printing .
            </p>
            <div class="ak-section-caption">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="223"
                  height="12"
                  viewBox="0 0 223 12"
                  fill="none"
                >
                  <path
                    d="M1.33789 1.18359H221.034L209.173 10.9822"
                    stroke="#FF4A23"
                    stroke-linecap="round"
                  />
                </svg>
              </span>
              <span> Top Trio Services </span>
            </div>
          </div>
        </div>
        <div class="ak-height-75 ak-height-lg-50"></div>
        <div class="core-features">
          <div
            class="core-feature-card type-3 fade-animation theme-border-wrap"
            data-delay="0.15"
          >
            <div class="b-top-left">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-top-right d-flex">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-right d-flex flex-end">
              <div class="horizontal flex-end align-self-end"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-left">
              <div class="verticle"></div>
              <div class="horizontal"></div>
            </div>
            <div class="icon">
              <i class="flaticon-circle"></i>
            </div>
            <h6 class="core-feature-title">Strategic Marketing</h6>
            <p class="core-feature-desp">
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been industry.
            </p>
            <p class="core-feature-number">01</p>
          </div>
          <div
            class="core-feature-card type-3 fade-animation theme-border-wrap"
            data-delay="0.35"
          >
            <div class="b-top-left">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-top-right d-flex">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-right d-flex flex-end">
              <div class="horizontal flex-end align-self-end"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-left">
              <div class="verticle"></div>
              <div class="horizontal"></div>
            </div>
            <div class="icon">
              <i class="flaticon-folded"></i>
            </div>
            <h6 class="core-feature-title">Strategic Marketing</h6>
            <p class="core-feature-desp">
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been industry.
            </p>
            <p class="core-feature-number">02</p>
          </div>
          <div
            class="core-feature-card type-3 fade-animation theme-border-wrap"
            data-delay="0.55"
          >
            <div class="b-top-left">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-top-right d-flex">
              <div class="horizontal"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-right d-flex flex-end">
              <div class="horizontal flex-end align-self-end"></div>
              <div class="verticle"></div>
            </div>
            <div class="b-bottom-left">
              <div class="verticle"></div>
              <div class="horizontal"></div>
            </div>
            <div class="icon">
              <i class="flaticon-twirl"></i>
            </div>
            <h6 class="core-feature-title">Strategic Marketing</h6>
            <p class="core-feature-desp">
              Lorem Ipsum is simply dummy text of the printing and typesetting
              industry. Lorem Ipsum has been industry.
            </p>
            <p class="core-feature-number">03</p>
          </div>
        </div>
      </div>
      <div class="ak-height-150 ak-height-lg-80"></div>
    </section>
    <!-- Start Core Feature -->

    <!-- Start Testmonial -->
    <section>
      <div class="container">
        <div class="testmonial-slider ak-slider">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div class="testmonial-content style-1">
                <div class="testmonial-card">
                  <div class="ak-height-100 ak-height-lg-50"></div>
                  <p class="testmonial-desp">
                    “Working with them has been an absolute game-changer for our
                    business. Their innovative strategies, coupled with their
                    deep understanding of our industry, have significantly
                    boosted our online presence.”
                  </p>
                  <div class="ak-height-50 ak-height-lg-30"></div>
                  <div class="testmonial-info">
                    <img
                      class="testmonial-img"
                      src="assets/img/testmonial/testmonial-1.png"
                      alt=".."
                    />
                    <h6 class="testmonial-title">Mostahid Jackma</h6>
                    <p class="testmonial-shot-desp">From USA</p>
                  </div>
                  <div class="ak-height-100 ak-height-lg-50"></div>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="testmonial-content style-1">
                <div class="testmonial-card">
                  <div class="ak-height-100 ak-height-lg-50"></div>
                  <p class="testmonial-desp">
                    “Working with them has been an absolute game-changer for our
                    business. Their innovative strategies, coupled with their
                    deep understanding of our industry, have significantly
                    boosted our online presence.”
                  </p>
                  <div class="ak-height-50 ak-height-lg-30"></div>
                  <div class="testmonial-info">
                    <img
                      class="testmonial-img"
                      src="assets/img/testmonial/testmonial-2.png"
                      alt=".."
                    />
                    <h6 class="testmonial-title">Morgan Brown</h6>
                    <p class="testmonial-shot-desp">From USA</p>
                  </div>
                  <div class="ak-height-100 ak-height-lg-50"></div>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="testmonial-content style-1">
                <div class="testmonial-card">
                  <div class="ak-height-100 ak-height-lg-50"></div>
                  <p class="testmonial-desp">
                    “Working with them has been an absolute game-changer for our
                    business. Their innovative strategies, coupled with their
                    deep understanding of our industry, have significantly
                    boosted our online presence.”
                  </p>
                  <div class="ak-height-50 ak-height-lg-30"></div>
                  <div class="testmonial-info">
                    <img
                      class="testmonial-img"
                      src="assets/img/testmonial/testmonial-1.png"
                      alt=".."
                    />
                    <h6 class="testmonial-title">Mostahid Jackma</h6>
                    <p class="testmonial-shot-desp">From USA</p>
                  </div>
                  <div class="ak-height-100 ak-height-lg-50"></div>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="testmonial-content style-1">
                <div class="testmonial-card">
                  <div class="ak-height-100 ak-height-lg-50"></div>
                  <p class="testmonial-desp">
                    “Working with them has been an absolute game-changer for our
                    business. Their innovative strategies, coupled with their
                    deep understanding of our industry, have significantly
                    boosted our online presence.”
                  </p>
                  <div class="ak-height-50 ak-height-lg-30"></div>
                  <div class="testmonial-info">
                    <img
                      class="testmonial-img"
                      src="assets/img/testmonial/testmonial-2.png"
                      alt=".."
                    />
                    <h6 class="testmonial-title">Morgan Brown</h6>
                    <p class="testmonial-shot-desp">From USA</p>
                  </div>
                  <div class="ak-height-100 ak-height-lg-50"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="testmonial-swiper-controller">
          <div class="testmonial-button-prev">
            <div>
              <span class="svg-icon">
                <i class="flaticon-left-up"></i>
              </span>
              <span> Previous </span>
            </div>
          </div>
          <div class="testmonial-button-next">
            <div>
              <span> Next </span>
              <span class="svg-icon">
                <i class="flaticon-up-right-arrow"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End Testmonial -->

    <!-- Start Newsletter -->
    <div class="ak-height-150 ak-height-lg-80"></div>
    <section class="container">
      <div class="newsletter-content style-2">
        <div class="newsletter-title-content">
          <h2 class="newsletter-title text-animation">
            Join Our
            <span class="highlight text-underlines underline-anim">
              Newsletter</span
            >
            for the Latest <span class="highlight">Exclusive</span> Content
          </h2>
        </div>

        <form class="newsletter-form">
          <input
            type="email"
            class="newsletter-input style-2"
            placeholder="Enter your email..."
            required
          />
          <button type="submit" class="newsletter-btn">
            <span class="newbtn-text"> Newsletter </span>
            <span class="primary-icon-anim">
              <i class="flaticon-up-right-arrow"></i>
              <i class="flaticon-up-right-arrow"></i>
            </span>
          </button>
        </form>
      </div>
    </section>
    <!-- End Newsletter -->

    

    <div class="mode-toggle">
      <div class="setting-mode">
        <button id="open">
          <i class="flaticon-sun"></i>
        </button>
        <button id="clecel">
          <i class="flaticon-close-button-1"></i>
        </button>
      </div>
      <div class="mode-btn js-mode-type">
        <button data-mode="light" class="mode-light">
          <i class="flaticon-sun"></i>
        </button>
        <button data-mode="dark" class="active mode-dark">
          <i class="flaticon-night-mode"></i>
        </button>
      </div>
    </div>

    <span class="ak-scrollup">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z"
          fill="currentColor"
        />
      </svg>
    </span>
    <!-- Start Video Popup -->
    <div class="ak-video-popup">
      <div class="ak-video-popup-overlay"></div>
      <div class="ak-video-popup-content">
        <div class="ak-video-popup-layer"></div>
        <div class="ak-video-popup-container">
          <div class="ak-video-popup-align">
            <div class="embed-responsive embed-responsive-16by9">
              <iframe class="embed-responsive-item" src="about:blank"></iframe>
            </div>
          </div>
          <div class="ak-video-popup-close"></div>
        </div>
      </div>
    </div>
    <!-- End Video Popup -->

    <!-- Script -->
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer"></div>
    
    <!-- Mode Toggle -->
    <div class="mode-toggle">
        <div class="setting-mode">
            <button id="open">
                <i class="flaticon-sun"></i>
            </button>
            <button id="clecel">
                <i class="flaticon-close-button-1"></i>
            </button>
        </div>
        <div class="mode-btn js-mode-type">
            <button data-mode="light" class="mode-light">
                <i class="flaticon-sun"></i>
            </button>
            <button data-mode="dark" class="active mode-dark">
                <i class="flaticon-night-mode"></i>
            </button>
        </div>
    </div>
    
    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
        </svg>
    </span>
    
    <!-- Video Popup -->
    <div class="ak-video-popup">
        <div class="ak-video-popup-overlay"></div>
        <div class="ak-video-popup-content">
            <div class="ak-video-popup-layer"></div>
            <div class="ak-video-popup-container">
                <div class="ak-video-popup-align">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="ak-video-popup-close"></div>
            </div>
        </div>
    </div>
    
<!-- Master JavaScript Template for All Pages (Except index.html) -->
<!-- This provides consistent functionality across all pages -->

<!-- Core Scripts -->
<script src="assets/js/plugins/jquery-3.7.1.min.js"></script>
<script src="assets/js/plugins/bootstrap.bundle.min.js"></script>
<script src="assets/js/plugins/swiper.min.js"></script>
<script src="assets/js/plugins/gsap.js"></script>
<script src="assets/js/plugins/lenis.min.js"></script>
<script src="assets/js/plugins/splittext.js"></script>
<script src="assets/js/plugins/scrolltigger.js"></script>
<script src="assets/js/plugins/scrolltoplugins.js"></script>
<script src="assets/js/main.js"></script>
<script src="assets/js/component-loader.js"></script>

<!-- Global Enhanced Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Global Variables
  const pageType = 'services';
  const pageName = 'Services';
  
  // Enhanced Preloader with Progress Animation
  const preloader = document.getElementById('preloader');
  const loadingPercent = document.getElementById('loadingPercent');
  const svgText = document.getElementById('svgText');
  
  if (preloader && loadingPercent) {
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(progressInterval);
        
        // Hide preloader after animation completes
        setTimeout(() => {
          preloader.style.opacity = '0';
          setTimeout(() => {
            preloader.style.display = 'none';
            // Initialize page content animations
            initPageAnimations();
          }, 500);
        }, 500);
      }
      loadingPercent.textContent = Math.floor(progress) + '%';
    }, 100);
  }
  
  // Global Page Animations
  function initPageAnimations() {
    // Fade in main content
    const pageContent = document.querySelector('.page-content') || document.querySelector('main');
    if (pageContent) {
      pageContent.classList.add('loaded');
    }
    
    // Initialize image animations
    initImageAnimations();
    
    // Initialize reading progress
    initReadingProgress();
    
    // Initialize smooth scrolling
    initSmoothScrolling();
  }
  
  // Global Image Loading Animations
  function initImageAnimations() {
    const images = document.querySelectorAll('img:not(.no-animate)');
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('img-animate');
          setTimeout(() => {
            entry.target.classList.add('loaded');
          }, 100);
          imageObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });
    
    images.forEach(img => {
      if (img.complete && img.naturalHeight !== 0) {
        img.classList.add('img-animate', 'loaded');
      } else {
        img.addEventListener('load', () => {
          imageObserver.observe(img);
        });
        // Fallback for images that might already be cached
        if (img.complete) {
          imageObserver.observe(img);
        }
      }
    });
  }
  
  // Global Reading Progress Bar
  function initReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.body.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      progressBar.style.width = Math.min(scrollPercent, 100) + '%';
    });
  }
  
  // Global Smooth Scrolling for Internal Links
  function initSmoothScrolling() {
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }
  
  // Global Accordion Enhancement (if present)
  const accordionItems = document.querySelectorAll('.ak-accordion-item');
  if (accordionItems.length > 0) {
    accordionItems.forEach(item => {
      const titleContent = item.querySelector('.ak-accordion-title-content');
      const tab = item.querySelector('.ak-accordion-tab');
      
      if (titleContent && tab) {
        titleContent.addEventListener('click', () => {
          // Close all other accordion items
          accordionItems.forEach(otherItem => {
            if (otherItem !== item) {
              otherItem.querySelector('.ak-accordion-title-content').classList.remove('active');
              const otherTab = otherItem.querySelector('.ak-accordion-tab');
              if (otherTab) otherTab.style.maxHeight = null;
            }
          });
          
          // Toggle current item
          titleContent.classList.toggle('active');
          
          if (titleContent.classList.contains('active')) {
            tab.style.maxHeight = tab.scrollHeight + 'px';
          } else {
            tab.style.maxHeight = null;
          }
        });
      }
    });
  }
  
  // Global Performance Tracking
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      console.log(`${pageName} page loaded in ${Math.round(loadTime)}ms`);
      
      // Send analytics if available
      if (typeof gtag !== 'undefined') {
        gtag('event', 'page_load_time', {
          event_category: 'Performance',
          event_label: pageName,
          value: Math.round(loadTime)
        });
      }
    });
  }
  
  // Global Error Handling
  window.addEventListener('error', (e) => {
    console.error('Global error on', pageName, ':', e.error);
    
    // Send error to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: e.error.toString(),
        fatal: false
      });
    }
  });
});

// Component Loading Event Listeners
document.addEventListener('componentLoaded', function(event) {
  const { elementId } = event.detail;
  
  if (elementId === 'header') {
    // Initialize header-specific functionality
    console.log('Header loaded and ready');
    
    // Add active class to current page navigation
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.ak-nav_list a');
    
    navLinks.forEach(link => {
      const linkHref = link.getAttribute('href');
      if (linkHref === currentPage || 
          (currentPage === 'index.html' && linkHref === '/') ||
          (currentPage === '' && linkHref === 'index.html')) {
        link.parentElement.classList.add('current-menu-item');
      }
    });
    
    // Initialize mobile menu if present
    const mobileMenuToggle = document.querySelector('.offcanvaopen-btn');
    if (mobileMenuToggle) {
      console.log('Mobile menu initialized');
    }
  }
  
  if (elementId === 'footer') {
    // Initialize footer-specific functionality
    console.log('Footer loaded and ready');
    
    // Update copyright year
    const copyrightYear = document.querySelector('.copyright-year');
    if (copyrightYear) {
      copyrightYear.textContent = new Date().getFullYear();
    }
  }
});

// Error Handling for Component Loading
document.addEventListener('componentLoadError', function(event) {
  console.warn('Component failed to load:', event.detail);
  
  // Provide fallback functionality
  if (event.detail.elementId === 'header') {
    const headerElement = document.getElementById('header');
    if (headerElement) {
      headerElement.innerHTML = `
        <header style="padding: 20px; background: var(--body-bg-color); border-bottom: 1px solid var(--border-color);">
          <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
            <a href="index.html" style="font-size: 24px; font-weight: bold; color: var(--primary-color); text-decoration: none;">Artix</a>
            <nav>
              <a href="index.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Home</a>
              <a href="about.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">About</a>
              <a href="services.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Services</a>
              <a href="portfolio.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Portfolio</a>
              <a href="contact.html" style="margin: 0 15px; color: var(--heading-color); text-decoration: none;">Contact</a>
            </nav>
          </div>
        </header>
      `;
    }
  }
  
  if (event.detail.elementId === 'footer') {
    const footerElement = document.getElementById('footer');
    if (footerElement) {
      footerElement.innerHTML = `
        <footer style="padding: 40px 20px; background: var(--body-bg-color); border-top: 1px solid var(--border-color); text-align: center;">
          <div style="max-width: 1200px; margin: 0 auto;">
            <p style="color: var(--body-color); margin: 0;">&copy; ${new Date().getFullYear()} Artix Digital Agency. All rights reserved.</p>
          </div>
        </footer>
      `;
    }
  }
});

// Global Utility Functions
window.ArtixGlobal = {
  // Smooth scroll to element
  scrollTo: function(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },
  
  // Show loading state
  showLoading: function(element) {
    if (element) {
      element.style.opacity = '0.5';
      element.style.pointerEvents = 'none';
    }
  },
  
  // Hide loading state
  hideLoading: function(element) {
    if (element) {
      element.style.opacity = '1';
      element.style.pointerEvents = 'auto';
    }
  },
  
  // Get page info
  getPageInfo: function() {
    return {
      type: 'services',
      name: 'Services',
      url: window.location.href,
      title: document.title
    };
  }
};
</script>

</body>
</html>