#!/usr/bin/env python3
"""
Global Enhancement Automation Script
Applies enhanced head section, preloader, and component loading to all HTML pages except index.html
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple

class GlobalEnhancementAutomator:
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir)
        self.templates_dir = self.root_dir / "templates"
        self.backup_dir = self.root_dir / "backups"
        
        # Page configurations
        self.page_configs = {
            "about.html": {
                "title": "About Us - Our Story & Mission",
                "description": "Learn about Artix Digital Agency - our story, mission, and the passionate team behind innovative digital solutions and creative designs.",
                "keywords": "about artix, digital agency team, our story, mission, creative professionals, web design team",
                "type": "about",
                "name": "About"
            },
            "services.html": {
                "title": "Our Services - Digital Solutions & Creative Design",
                "description": "Explore Artix's comprehensive digital services: web development, mobile apps, branding, UI/UX design, and digital marketing solutions.",
                "keywords": "digital services, web development, mobile apps, branding, UI UX design, digital marketing",
                "type": "services",
                "name": "Services"
            },
            "service-details.html": {
                "title": "Service Details - Android & iOS App Development",
                "description": "Discover our exceptional Android & iOS app development services. Professional digital solutions for your business growth.",
                "keywords": "app development, android apps, ios apps, mobile development, custom apps",
                "type": "service-details",
                "name": "Service Details"
            },
            "portfolio.html": {
                "title": "Portfolio - Our Creative Work & Projects",
                "description": "Explore Artix's portfolio showcasing our best creative work, successful projects, and innovative digital solutions for clients worldwide.",
                "keywords": "portfolio, creative work, projects, case studies, web design portfolio, app development showcase",
                "type": "portfolio",
                "name": "Portfolio"
            },
            "portfolio-details.html": {
                "title": "Portfolio Details - Project Case Study",
                "description": "Detailed case study of our creative project showcasing our design process, challenges overcome, and successful results achieved.",
                "keywords": "portfolio details, case study, project details, design process, creative solutions",
                "type": "portfolio-details",
                "name": "Portfolio Details"
            },
            "contact.html": {
                "title": "Contact Us - Get In Touch",
                "description": "Contact Artix Digital Agency for your next project. Get in touch with our team for professional digital solutions and creative services.",
                "keywords": "contact artix, get in touch, digital agency contact, project inquiry, consultation",
                "type": "contact",
                "name": "Contact"
            },
            "blog.html": {
                "title": "Blog - Digital Insights & Creative Tips",
                "description": "Read Artix's blog for the latest insights on digital trends, creative design tips, web development best practices, and industry news.",
                "keywords": "digital blog, design tips, web development blog, creative insights, industry trends",
                "type": "blog",
                "name": "Blog"
            },
            "blog-details.html": {
                "title": "Blog Post - Digital Insights & Tips",
                "description": "Read our latest blog post with valuable insights on digital design, development trends, and creative industry best practices.",
                "keywords": "blog post, digital insights, design tips, development trends, creative industry",
                "type": "blog-details",
                "name": "Blog Details"
            },
            "team.html": {
                "title": "Our Team - Meet the Creative Professionals",
                "description": "Meet the talented team behind Artix Digital Agency. Our creative professionals, developers, and designers who bring your vision to life.",
                "keywords": "artix team, creative professionals, web developers, designers, digital agency team",
                "type": "team",
                "name": "Team"
            },
            "pricing.html": {
                "title": "Pricing - Transparent & Competitive Rates",
                "description": "Explore Artix's transparent pricing for digital services. Competitive rates for web development, app development, and creative design services.",
                "keywords": "pricing, digital services cost, web development pricing, app development rates, design services cost",
                "type": "pricing",
                "name": "Pricing"
            },
            "faq.html": {
                "title": "FAQ - Frequently Asked Questions",
                "description": "Find answers to frequently asked questions about Artix's digital services, project process, timelines, and support.",
                "keywords": "FAQ, frequently asked questions, digital services FAQ, project process, support",
                "type": "faq",
                "name": "FAQ"
            },
            "404.html": {
                "title": "Page Not Found - 404 Error",
                "description": "The page you're looking for doesn't exist. Return to Artix Digital Agency homepage or explore our services.",
                "keywords": "404 error, page not found, artix digital agency",
                "type": "error",
                "name": "404 Error"
            },
            "coming-soon.html": {
                "title": "Coming Soon - Exciting Updates Ahead",
                "description": "Something exciting is coming soon from Artix Digital Agency. Stay tuned for our latest updates and new features.",
                "keywords": "coming soon, artix updates, new features, digital agency news",
                "type": "coming-soon",
                "name": "Coming Soon"
            }
        }
        
        # Files to exclude from processing
        self.excluded_files = {
            "index.html",  # Perfect as-is, don't modify
            "component-demo.html",  # Demo file
            "page-template.html"  # Template file
        }
    
    def create_backup(self, file_path: Path) -> Path:
        """Create backup of original file"""
        self.backup_dir.mkdir(exist_ok=True)
        backup_path = self.backup_dir / f"{file_path.name}.backup"
        
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Backup created: {backup_path}")
        
        return backup_path
    
    def load_template(self, template_name: str) -> str:
        """Load template content"""
        template_path = self.templates_dir / template_name
        
        if not template_path.exists():
            raise FileNotFoundError(f"Template not found: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def replace_placeholders(self, content: str, config: Dict[str, str]) -> str:
        """Replace placeholders in template content"""
        replacements = {
            "{{PAGE_TITLE}}": config["title"],
            "{{PAGE_DESCRIPTION}}": config["description"],
            "{{PAGE_KEYWORDS}}": config["keywords"],
            "{{PAGE_TYPE}}": config["type"],
            "{{PAGE_NAME}}": config["name"],
            "{{PAGE_URL}}": f"{config['type']}.html"
        }
        
        result = content
        for placeholder, value in replacements.items():
            result = result.replace(placeholder, value)
        
        return result
    
    def extract_body_content(self, html_content: str) -> str:
        """Extract content between <body> and </body> tags, excluding head and scripts"""
        # Find body opening tag
        body_start_match = re.search(r'<body[^>]*>', html_content, re.IGNORECASE)
        if not body_start_match:
            return ""
        
        body_start = body_start_match.end()
        
        # Find body closing tag
        body_end_match = re.search(r'</body>', html_content, re.IGNORECASE)
        if not body_end_match:
            body_end = len(html_content)
        else:
            body_end = body_end_match.start()
        
        body_content = html_content[body_start:body_end]
        
        # Remove existing preloader, header, and footer placeholders if present
        body_content = re.sub(r'<div id="preloader[^"]*"[^>]*>.*?</div>', '', body_content, flags=re.DOTALL)
        body_content = re.sub(r'<div id="header"[^>]*></div>', '', body_content)
        body_content = re.sub(r'<div id="footer"[^>]*></div>', '', body_content)
        body_content = re.sub(r'<div id="preloader-placeholder"[^>]*></div>', '', body_content)
        
        # Remove existing script tags at the end
        body_content = re.sub(r'<script[^>]*>.*?</script>', '', body_content, flags=re.DOTALL)
        
        return body_content.strip()
    
    def generate_enhanced_html(self, original_content: str, config: Dict[str, str]) -> str:
        """Generate enhanced HTML with templates"""
        # Load templates
        head_template = self.load_template("master-head.html")
        scripts_template = self.load_template("master-scripts.html")
        
        # Replace placeholders
        enhanced_head = self.replace_placeholders(head_template, config)
        enhanced_scripts = self.replace_placeholders(scripts_template, config)
        
        # Extract body content
        body_content = self.extract_body_content(original_content)
        
        # Extract body class if present
        body_class_match = re.search(r'<body[^>]*class="([^"]*)"', original_content, re.IGNORECASE)
        body_class = body_class_match.group(1) if body_class_match else "dark"
        
        # Generate enhanced HTML
        enhanced_html = f'''<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
{enhanced_head}
</head>

<body class="{body_class}">
    <!-- Enhanced Preloader -->
    <div id="preloader" class="preloader">
      <div class="txt-loading">
        <div class="preloader-text">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 800 300"
            class="preloader-text-svg"
          >
            <defs>
              <linearGradient
                id="textGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stop-color="#FF4A23" />
                <stop offset="50%" stop-color="#FF6B47" />
                <stop offset="100%" stop-color="#FF4A23" />
              </linearGradient>
            </defs>
            <text class="svg-text" id="svgText" x="50%" y="50%" text-anchor="middle" dominant-baseline="middle">Artix</text>
          </svg>
        </div>
        <div class="loading-percent" id="loadingPercent">0%</div>
      </div>
    </div>
    <!-- End Enhanced Preloader -->
    
    <!-- Dynamic Header Placeholder -->
    <div id="header"></div>
    
    <!-- Page Content -->
    <main class="page-content">
{body_content}
    </main>
    
    <!-- Dynamic Footer Placeholder -->
    <div id="footer"></div>
    
    <!-- Mode Toggle -->
    <div class="mode-toggle">
        <div class="setting-mode">
            <button id="open">
                <i class="flaticon-sun"></i>
            </button>
            <button id="clecel">
                <i class="flaticon-close-button-1"></i>
            </button>
        </div>
        <div class="mode-btn js-mode-type">
            <button data-mode="light" class="mode-light">
                <i class="flaticon-sun"></i>
            </button>
            <button data-mode="dark" class="active mode-dark">
                <i class="flaticon-night-mode"></i>
            </button>
        </div>
    </div>
    
    <!-- Scroll Up Button -->
    <span class="ak-scrollup">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 10L1.7625 11.7625L8.75 4.7875V20H11.25V4.7875L18.225 11.775L20 10L10 0L0 10Z" fill="currentColor" />
        </svg>
    </span>
    
    <!-- Video Popup -->
    <div class="ak-video-popup">
        <div class="ak-video-popup-overlay"></div>
        <div class="ak-video-popup-content">
            <div class="ak-video-popup-layer"></div>
            <div class="ak-video-popup-container">
                <div class="ak-video-popup-align">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="ak-video-popup-close"></div>
            </div>
        </div>
    </div>
    
{enhanced_scripts}
</body>
</html>'''
        
        return enhanced_html
    
    def process_file(self, file_path: Path) -> bool:
        """Process a single HTML file"""
        try:
            # Skip excluded files
            if file_path.name in self.excluded_files:
                print(f"⏭️  Skipping {file_path.name} (excluded)")
                return True
            
            # Check if we have config for this file
            if file_path.name not in self.page_configs:
                print(f"⚠️  No config found for {file_path.name}, skipping")
                return False
            
            print(f"🔄 Processing {file_path.name}...")
            
            # Create backup
            self.create_backup(file_path)
            
            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Generate enhanced HTML
            config = self.page_configs[file_path.name]
            enhanced_html = self.generate_enhanced_html(original_content, config)
            
            # Write enhanced content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_html)
            
            print(f"✅ Enhanced {file_path.name}")
            return True
            
        except Exception as e:
            print(f"❌ Error processing {file_path.name}: {str(e)}")
            return False
    
    def run(self) -> Dict[str, List[str]]:
        """Run the global enhancement automation"""
        print("🚀 Starting Global Enhancement Automation...")
        print("=" * 50)
        
        # Find all HTML files
        html_files = list(self.root_dir.glob("*.html"))
        
        results = {
            "processed": [],
            "skipped": [],
            "errors": []
        }
        
        for file_path in html_files:
            if file_path.name in self.excluded_files:
                results["skipped"].append(file_path.name)
                print(f"⏭️  Skipping {file_path.name} (excluded)")
                continue
            
            if self.process_file(file_path):
                results["processed"].append(file_path.name)
            else:
                results["errors"].append(file_path.name)
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 AUTOMATION SUMMARY")
        print("=" * 50)
        print(f"✅ Processed: {len(results['processed'])} files")
        print(f"⏭️  Skipped: {len(results['skipped'])} files")
        print(f"❌ Errors: {len(results['errors'])} files")
        
        if results["processed"]:
            print(f"\n🎉 Successfully enhanced: {', '.join(results['processed'])}")
        
        if results["errors"]:
            print(f"\n⚠️  Files with errors: {', '.join(results['errors'])}")
        
        print(f"\n💾 Backups saved in: {self.backup_dir}")
        print("🎯 Global enhancement automation completed!")
        
        return results

    def validate_enhanced_file(self, file_path: Path) -> Dict[str, bool]:
        """Validate that enhanced file has all required components"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            checks = {
                "has_enhanced_head": bool(re.search(r'<!-- Master Head Template', content)),
                "has_preloader": bool(re.search(r'id="preloader"', content)),
                "has_header_placeholder": bool(re.search(r'id="header"', content)),
                "has_footer_placeholder": bool(re.search(r'id="footer"', content)),
                "has_component_loader": bool(re.search(r'component-loader\.js', content)),
                "has_artix_branding": bool(re.search(r'Artix', content)),
                "has_meta_tags": bool(re.search(r'<meta name="description"', content)),
                "has_og_tags": bool(re.search(r'<meta property="og:', content)),
                "has_structured_data": bool(re.search(r'application/ld\+json', content)),
                "has_reading_progress": bool(re.search(r'reading-progress', content))
            }

            return checks

        except Exception as e:
            print(f"❌ Validation error for {file_path.name}: {str(e)}")
            return {}

    def run_quality_assurance(self) -> Dict[str, any]:
        """Run quality assurance checks on all enhanced files"""
        print("\n🔍 Running Quality Assurance Checks...")
        print("=" * 50)

        html_files = [f for f in self.root_dir.glob("*.html")
                     if f.name not in self.excluded_files and f.name in self.page_configs]

        qa_results = {
            "total_files": len(html_files),
            "passed": 0,
            "failed": 0,
            "details": {}
        }

        for file_path in html_files:
            print(f"🔍 Checking {file_path.name}...")
            checks = self.validate_enhanced_file(file_path)

            if checks:
                passed_checks = sum(checks.values())
                total_checks = len(checks)
                success_rate = (passed_checks / total_checks) * 100

                qa_results["details"][file_path.name] = {
                    "checks": checks,
                    "passed": passed_checks,
                    "total": total_checks,
                    "success_rate": success_rate
                }

                if success_rate >= 90:
                    qa_results["passed"] += 1
                    print(f"✅ {file_path.name}: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")
                else:
                    qa_results["failed"] += 1
                    print(f"⚠️  {file_path.name}: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")

                    # Show failed checks
                    failed_checks = [check for check, passed in checks.items() if not passed]
                    if failed_checks:
                        print(f"   Failed: {', '.join(failed_checks)}")
            else:
                qa_results["failed"] += 1
                print(f"❌ {file_path.name}: Validation failed")

        # Print QA summary
        print("\n" + "=" * 50)
        print("📊 QUALITY ASSURANCE SUMMARY")
        print("=" * 50)
        print(f"✅ Passed: {qa_results['passed']}/{qa_results['total_files']} files")
        print(f"⚠️  Failed: {qa_results['failed']}/{qa_results['total_files']} files")

        overall_success = (qa_results['passed'] / qa_results['total_files']) * 100
        print(f"🎯 Overall Success Rate: {overall_success:.1f}%")

        return qa_results

if __name__ == "__main__":
    automator = GlobalEnhancementAutomator()

    # Run enhancement automation
    results = automator.run()

    # Run quality assurance
    qa_results = automator.run_quality_assurance()

    # Final summary
    print("\n" + "🎉" * 20)
    print("GLOBAL ENHANCEMENT COMPLETED!")
    print("🎉" * 20)
    print(f"📁 Enhanced Files: {len(results['processed'])}")
    print(f"✅ QA Passed: {qa_results['passed']}")
    print(f"💾 Backups: Available in {automator.backup_dir}")
    print("🚀 Your website is now globally enhanced!")
